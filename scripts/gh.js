#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const GitHub = require('github-api');
const axios = require('axios');

// Command line argument parsing
program
  .name('gh')
  .description('GitHub API utilities for sponsors data and contribution statistics')
  .option('--input <file>', 'Input JSON file path', 'data/solnic-sponsorships-all-time.json')
  .option('--output <file>', 'Output CSV file path', 'data/github-sponsors.csv')
  .option('--json', 'Also generate JSON output file')
  .option('--dry-run', 'Show what would be written without updating files')
  .option('--verbose', 'Show detailed output')
  .option('--stats', 'Fetch and display GitHub contribution statistics')
  .option('--stats-output <file>', 'Output file for contribution statistics', 'data/github-stats.json')
  .option('--username <username>', 'GitHub username for statistics (defaults to authenticated user)')
  .option('--repos', 'Fetch repository contribution statistics')
  .option('--repos-output <file>', 'Output file for repository contributions', 'data/github-repo-contributions.json')
  .option('--force', 'Force refresh of cached data')
  .addHelpText('after', `
Examples:
  Process exported sponsors data:
    $ GITHUB_API_KEY=your_token node scripts/gh.js

  Fetch contribution statistics:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --stats

  Fetch repository contribution data:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --repos

  Fetch stats for specific user:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --stats --username solnic

  Dry run to see what would be updated:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --dry-run

  Generate both CSV and JSON output:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --json

Environment Variables:
  GITHUB_API_KEY - Required GitHub Personal Access Token with 'user:read' scope

Note: This script processes manually exported sponsors data from GitHub Sponsors dashboard.
Export your sponsors data as JSON and place it in data/solnic-sponsorships-all-time.json
`)
  .parse();

const options = program.opts();

// Check for required environment variables and input file
const checkEnvironment = () => {
  const githubToken = process.env.GITHUB_API_KEY || process.env.GITHUB_TOKEN || process.env.GH_TOKEN;
  if (!githubToken) {
    console.error('❌ Missing required GitHub token environment variable');
    console.error('💡 Set one of: GITHUB_TOKEN, GITHUB_API_KEY, or GH_TOKEN');
    console.error('💡 Get a token from: https://github.com/settings/tokens');
    console.error('💡 Required scopes: repo (for private repos) or public_repo (for public repos only)');
    console.error('💡 Usage: GITHUB_TOKEN=your_token node scripts/gh.js --repos');
    process.exit(1);
  }

  if (options.verbose) {
    console.log(`✅ GitHub token found (${githubToken.substring(0, 8)}...)`);
  }

  const inputPath = path.resolve(options.input);
  if (!fs.existsSync(inputPath)) {
    console.error(`❌ Input file not found: ${inputPath}`);
    console.error('💡 Export your sponsors data from GitHub Sponsors dashboard as JSON');
    console.error('💡 Place the file at: data/solnic-sponsorships-all-time.json');
    process.exit(1);
  }

  return { inputPath };
};

// Initialize GitHub API client
const initializeGitHubClient = () => {
  try {
    const token = process.env.GITHUB_API_KEY;
    if (!token) {
      throw new Error('GITHUB_API_KEY environment variable not set');
    }

    const gh = new GitHub({
      token: token
    });

    if (options.verbose) {
      console.log('✅ GitHub API client initialized');
      console.log(`🔑 Token: ${token.substring(0, 8)}...${token.substring(token.length - 4)}`);
      console.log('⚠️  Note: Sponsors data is not accessible via API');
    }

    return gh;
  } catch (error) {
    console.error('❌ Failed to initialize GitHub API client:', error.message);
    process.exit(1);
  }
};

// Get user information and validate API access
const validateAPIAccess = async (gh) => {
  try {
    console.log('📊 Validating GitHub API access...');

    const user = gh.getUser();
    const { data: userInfo } = await user.getProfile();

    if (options.verbose) {
      console.log(`✅ Authenticated as: ${userInfo.login} (${userInfo.name || 'No name'})`);
    }

    return userInfo;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      throw new Error('GitHub API authentication failed. Check your GITHUB_API_KEY.');
    } else if (error.response && error.response.status === 403) {
      throw new Error('GitHub API rate limit exceeded or insufficient permissions.');
    } else {
      throw new Error(`GitHub API error: ${error.message}`);
    }
  }
};

// Fetch GitHub profile data for a sponsor
const fetchSponsorProfile = async (gh, handle) => {
  try {
    const user = gh.getUser(handle);
    const { data: profile } = await user.getProfile();

    return {
      login: profile.login,
      name: profile.name || profile.login,
      avatar_url: profile.avatar_url
    };
  } catch (error) {
    if (options.verbose) {
      console.warn(`⚠️  Could not fetch profile for ${handle}: ${error.message}`);
    }

    // Return fallback data
    return {
      login: handle,
      name: handle,
      avatar_url: `https://github.com/${handle}.png`
    };
  }
};

// Process sponsors data and fetch GitHub profiles
const processSponsorsData = async (gh, sponsorsData) => {
  console.log('🔍 Processing sponsors data and fetching GitHub profiles...');

  const processedSponsors = [];

  for (const sponsor of sponsorsData) {
    if (options.verbose) {
      console.log(`📄 Processing sponsor: ${sponsor.sponsor_handle}`);
    }

    // Fetch GitHub profile data
    const profile = await fetchSponsorProfile(gh, sponsor.sponsor_handle);

    // Determine if sponsor is active
    const isActive = isSponsorActive(sponsor);

    // Get latest tier info
    const tierInfo = getLatestTierInfo(sponsor);

    // Format sponsorship start date
    const startDate = new Date(sponsor.sponsorship_started_on).toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '');

    processedSponsors.push({
      sponsor_handle: profile.login,
      sponsor_name: profile.name,
      avatar_url: profile.avatar_url,
      is_active: isActive,
      is_public: sponsor.is_public || false,
      sponsorship_started_on: startDate,
      tier_name: tierInfo.tier_name,
      tier_monthly_price_in_dollars: tierInfo.tier_monthly_price_in_dollars
    });

    // Add small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`✅ Processed ${processedSponsors.length} sponsors`);
  return processedSponsors;
};

// Read and parse the exported sponsors JSON file
const readSponsorsData = (inputPath) => {
  try {
    console.log(`📄 Reading sponsors data from: ${inputPath}`);
    const jsonContent = fs.readFileSync(inputPath, 'utf8');
    const sponsorsData = JSON.parse(jsonContent);

    if (!Array.isArray(sponsorsData)) {
      throw new Error('Expected sponsors data to be an array');
    }

    console.log(`✅ Found ${sponsorsData.length} sponsors in export file`);
    return sponsorsData;
  } catch (error) {
    console.error('❌ Failed to read sponsors data:', error.message);
    process.exit(1);
  }
};

// Determine if a sponsor is currently active based on recent transactions
const isSponsorActive = (sponsor) => {
  if (!sponsor.transactions || sponsor.transactions.length === 0) {
    return false;
  }

  // Sort transactions by date (most recent first)
  const sortedTransactions = sponsor.transactions
    .filter(t => t.status === 'settled' || t.status === 'credit_balance_adjusted')
    .sort((a, b) => new Date(b.transaction_date) - new Date(a.transaction_date));

  if (sortedTransactions.length === 0) {
    return false;
  }

  // Check if the most recent transaction was within the last 45 days
  const mostRecentTransaction = sortedTransactions[0];
  const transactionDate = new Date(mostRecentTransaction.transaction_date);
  const now = new Date();
  const daysSinceLastTransaction = (now - transactionDate) / (1000 * 60 * 60 * 24);

  return daysSinceLastTransaction <= 45;
};

// Get the most recent tier information for a sponsor
const getLatestTierInfo = (sponsor) => {
  if (!sponsor.transactions || sponsor.transactions.length === 0) {
    return { tier_name: '', tier_monthly_price_in_dollars: '' };
  }

  // Sort transactions by date (most recent first)
  const sortedTransactions = sponsor.transactions
    .filter(t => t.status === 'settled' || t.status === 'credit_balance_adjusted')
    .sort((a, b) => new Date(b.transaction_date) - new Date(a.transaction_date));

  if (sortedTransactions.length === 0) {
    return { tier_name: '', tier_monthly_price_in_dollars: '' };
  }

  const latestTransaction = sortedTransactions[0];
  return {
    tier_name: latestTransaction.tier_name || '',
    tier_monthly_price_in_dollars: latestTransaction.tier_monthly_amount ?
      latestTransaction.tier_monthly_amount.replace('$', '') : ''
  };
};

// Format sponsor data for CSV output
const formatSponsorDataForCSV = (sponsorsData) => {
  return sponsorsData.map(sponsor => ({
    sponsor_handle: sponsor.sponsor_handle,
    sponsor_name: sponsor.sponsor_name,
    avatar_url: sponsor.avatar_url,
    is_active: sponsor.is_active ? 'true' : 'false',
    is_public: sponsor.is_public ? 'true' : 'false',
    sponsorship_started_on: sponsor.sponsorship_started_on,
    tier_name: sponsor.tier_name,
    tier_monthly_price_in_dollars: sponsor.tier_monthly_price_in_dollars
  }));
};

// Convert data to CSV format
const generateCSV = (data) => {
  const headers = [
    'sponsor_handle',
    'sponsor_name',
    'avatar_url',
    'is_active',
    'is_public',
    'sponsorship_started_on',
    'tier_name',
    'tier_monthly_price_in_dollars'
  ];

  const csvLines = [headers.join(',')];

  data.forEach(row => {
    const csvRow = headers.map(header => {
      const value = row[header] || '';
      // Escape commas and quotes in CSV values
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    });
    csvLines.push(csvRow.join(','));
  });

  return csvLines.join('\n');
};

// Generate JSON content from sponsor data
const generateJSON = (sponsorData) => {
  const activeSponsors = sponsorData.filter(s => s.is_active);
  const pastSponsors = sponsorData.filter(s => !s.is_active);

  return JSON.stringify({
    generated_at: new Date().toISOString(),
    total_sponsors: sponsorData.length,
    active_sponsors: activeSponsors.length,
    past_sponsors: pastSponsors.length,
    sponsors: {
      active: activeSponsors,
      past: pastSponsors,
      all: sponsorData
    }
  }, null, 2);
};

// GraphQL query for GitHub contribution statistics
const makeGraphQLRequest = async (query, variables = {}) => {
  try {
    const response = await axios.post('https://api.github.com/graphql', {
      query,
      variables
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.GITHUB_API_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'gh-stats-script'
      }
    });

    if (response.data.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(response.data.errors)}`);
    }

    return response.data.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`GraphQL request failed: ${error.response.status} ${error.response.statusText}`);
    }
    throw error;
  }
};

// Get contribution statistics for a user
const getContributionStats = async (username) => {
  const query = `
    query($userName: String!) {
      user(login: $userName) {
        login
        name
        contributionsCollection {
          contributionCalendar {
            totalContributions
            weeks {
              contributionDays {
                contributionCount
                date
              }
            }
          }
        }
        repositories(first: 100, ownerAffiliations: [OWNER, COLLABORATOR, ORGANIZATION_MEMBER]) {
          totalCount
          nodes {
            name
            owner {
              login
            }
            isPrivate
          }
        }
        organizations(first: 100) {
          totalCount
          nodes {
            login
            name
          }
        }
      }
    }
  `;

  const data = await makeGraphQLRequest(query, { userName: username });

  if (!data.user) {
    throw new Error(`User ${username} not found`);
  }

  return data.user;
};

// Get historical contribution data for multiple years
const getHistoricalContributions = async (username, years = 5) => {
  const currentYear = new Date().getFullYear();
  const yearlyStats = [];

  for (let i = 0; i < years; i++) {
    const year = currentYear - i;
    const from = `${year}-01-01T00:00:00Z`;
    const to = `${year}-12-31T23:59:59Z`;

    const query = `
      query($userName: String!, $from: DateTime!, $to: DateTime!) {
        user(login: $userName) {
          contributionsCollection(from: $from, to: $to) {
            contributionCalendar {
              totalContributions
            }
          }
        }
      }
    `;

    try {
      const data = await makeGraphQLRequest(query, {
        userName: username,
        from,
        to
      });

      if (data.user) {
        yearlyStats.push({
          year,
          totalContributions: data.user.contributionsCollection.contributionCalendar.totalContributions
        });
      }

      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.warn(`⚠️  Could not fetch contributions for ${year}: ${error.message}`);
    }
  }

  return yearlyStats;
};



// Fetch and process GitHub contribution statistics
const fetchContributionStats = async () => {
  try {
    console.log('🚀 Fetching GitHub contribution statistics...');

    // Check for required environment variables
    if (!process.env.GITHUB_API_KEY) {
      console.error('❌ Missing required environment variable: GITHUB_API_KEY');
      console.error('💡 Get a token from: https://github.com/settings/tokens');
      console.error('💡 Required scopes: user:read');
      process.exit(1);
    }

    // Initialize GitHub API client for REST calls
    const gh = initializeGitHubClient();

    // Get authenticated user info if no username specified
    let username = options.username;
    if (!username) {
      const userInfo = await validateAPIAccess(gh);
      username = userInfo.login;
    }

    console.log(`📊 Fetching statistics for user: ${username}`);

    // Get current year contribution stats
    const currentStats = await getContributionStats(username);

    // Get historical contribution data
    console.log('📈 Fetching historical contribution data...');
    const historicalStats = await getHistoricalContributions(username, 16);

    // Calculate total contributions across all years
    const totalContributions = historicalStats.reduce((sum, year) => sum + year.totalContributions, 0);

    // Get additional repository statistics
    const publicRepos = currentStats.repositories.nodes.filter(repo => !repo.isPrivate);
    const contributedRepos = currentStats.repositories.totalCount;

    // Compile final statistics
    const stats = {
      generated_at: new Date().toISOString(),
      username: currentStats.login,
      name: currentStats.name,
      total_contributions: totalContributions,
      current_year_contributions: currentStats.contributionsCollection.contributionCalendar.totalContributions,
      total_repositories: contributedRepos,
      public_repositories: publicRepos.length,
      organizations_count: currentStats.organizations.totalCount,
      organizations: currentStats.organizations.nodes.map(org => ({
        login: org.login,
        name: org.name
      })),
      yearly_contributions: historicalStats,
      repositories_sample: publicRepos.slice(0, 20).map(repo => ({
        name: repo.name,
        owner: repo.owner.login,
        is_private: repo.isPrivate
      }))
    };

    // Output results
    const statsOutputPath = path.resolve(options.statsOutput);

    if (options.dryRun) {
      console.log('\n🔍 Dry run - would write statistics:');
      console.log(`📊 Total contributions (${historicalStats.length} years): ${totalContributions.toLocaleString()}`);
      console.log(`📊 Current year contributions: ${stats.current_year_contributions.toLocaleString()}`);
      console.log(`📊 Total repositories: ${stats.total_repositories}`);
      console.log(`📊 Public repositories: ${stats.public_repositories}`);
      console.log(`📊 Organizations: ${stats.organizations_count}`);
      console.log(`📄 Stats output file: ${statsOutputPath}`);
      console.log('\n🔍 Dry run completed - no files were written');
      return;
    }

    // Write statistics to file
    fs.writeFileSync(statsOutputPath, JSON.stringify(stats, null, 2));

    console.log('✅ GitHub contribution statistics fetched successfully!');
    console.log(`📊 Total contributions (${historicalStats.length} years): ${totalContributions.toLocaleString()}`);
    console.log(`📊 Current year contributions: ${stats.current_year_contributions.toLocaleString()}`);
    console.log(`📊 Total repositories: ${stats.total_repositories}`);
    console.log(`📊 Public repositories: ${stats.public_repositories}`);
    console.log(`📊 Organizations: ${stats.organizations_count}`);
    console.log(`💾 Statistics saved to: ${statsOutputPath}`);

    if (options.verbose) {
      console.log('\n📋 Organizations:');
      stats.organizations.forEach(org => {
        console.log(`   - ${org.name} (@${org.login})`);
      });

      console.log('\n📋 Yearly breakdown:');
      historicalStats.forEach(year => {
        console.log(`   - ${year.year}: ${year.totalContributions.toLocaleString()} contributions`);
      });
    }

  } catch (error) {
    console.error('❌ Error fetching contribution statistics:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Get all repositories from an organization where user has contributions
const getOrganizationRepositories = async (orgName, minStars = 10) => {
  try {
    const { Octokit } = require('@octokit/rest');
    const octokit = new Octokit({
      auth: process.env.GITHUB_API_KEY,
      userAgent: 'solnic.dev-data-fetcher'
    });

    if (options.verbose) {
      console.log(`🔍 Fetching repositories from ${orgName} organization...`);
    }

    // Get all repositories from the organization
    const { data: repos } = await octokit.rest.repos.listForOrg({
      org: orgName,
      type: 'public',
      sort: 'updated',
      per_page: 100
    });

    // Filter repositories where user likely has contributions
    // (based on stars, activity, and other heuristics)
    const relevantRepos = repos
      .filter(repo => {
        // Include repos with decent activity/stars
        return repo.stargazers_count >= minStars ||
               repo.forks_count >= 5 ||
               repo.name.includes('dry-') || // dry-rb specific
               repo.name.includes('rom-'); // rom-rb specific
      })
      .map(repo => repo.full_name);

    if (options.verbose) {
      console.log(`✅ Found ${relevantRepos.length} relevant repositories in ${orgName}`);
    }

    return relevantRepos;

  } catch (error) {
    console.error(`❌ Failed to fetch repositories from ${orgName}: ${error.message}`);
    return [];
  }
};

// Get list of repositories to analyze from project data + auto-discovery
const getRepositoriesToAnalyze = async () => {
  const projectsPath = path.join(__dirname, '..', 'data', 'open-source-projects.json');
  const projectsData = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));

  const repos = new Set();

  // Collect manually specified repositories from project data
  projectsData.projects.forEach(project => {
    if (project.highlightedRepoNames) {
      project.highlightedRepoNames.forEach(repo => repos.add(repo));
    }
  });

  projectsData.past_projects.forEach(project => {
    if (project.highlightedRepoNames) {
      project.highlightedRepoNames.forEach(repo => repos.add(repo));
    }
  });

  // Auto-discover repositories from key organizations
  const organizations = ['dry-rb', 'rom-rb', 'hanami'];

  for (const org of organizations) {
    try {
      const orgRepos = await getOrganizationRepositories(org);
      orgRepos.forEach(repo => repos.add(repo));
    } catch (error) {
      if (options.verbose) {
        console.log(`⚠️  Could not fetch repos from ${org}: ${error.message}`);
      }
    }
  }

  // Also add user's own repositories that might be relevant
  try {
    const { Octokit } = require('@octokit/rest');
    const octokit = new Octokit({
      auth: process.env.GITHUB_API_KEY,
      userAgent: 'solnic.dev-data-fetcher'
    });

    const { data: userRepos } = await octokit.rest.repos.listForAuthenticatedUser({
      type: 'public',
      sort: 'updated',
      per_page: 50
    });

    // Add user's own repos with decent activity
    userRepos
      .filter(repo => repo.stargazers_count >= 5 || repo.forks_count >= 2)
      .forEach(repo => repos.add(repo.full_name));

  } catch (error) {
    if (options.verbose) {
      console.log(`⚠️  Could not fetch user repositories: ${error.message}`);
    }
  }

  return Array.from(repos);
};

// Fetch detailed repository contribution statistics using Octokit
const fetchRepositoryContributions = async (repoFullName) => {
  const [owner, repo] = repoFullName.split('/');

  if (options.verbose) {
    console.log(`📊 Fetching contribution data for ${repoFullName}...`);
  }

  try {
    // Use Octokit instead of the older GitHub client
    const { Octokit } = require('@octokit/rest');
    const octokit = new Octokit({
      auth: process.env.GITHUB_API_KEY,
      userAgent: 'solnic.dev-data-fetcher'
    });

    if (options.verbose) {
      console.log(`🔍 Fetching details for ${owner}/${repo} using Octokit...`);
    }

    // Get basic repository information
    const { data: repoData } = await octokit.rest.repos.get({
      owner,
      repo
    });

    // Get contributor statistics using multiple approaches
    let userCommits = 0;
    let totalCommits = 0;
    let userContributionPercentage = 0;

    // Method 1: Try contributor stats API (often unreliable)
    try {
      const { data: contributors } = await octokit.rest.repos.getContributorsStats({
        owner,
        repo
      });

      if (Array.isArray(contributors) && contributors.length > 0) {
        // Calculate total commits
        totalCommits = contributors.reduce((sum, contrib) => sum + contrib.total, 0);

        // Find user's contributions (looking for 'solnic')
        const userContrib = contributors.find(contrib =>
          contrib.author && contrib.author.login === 'solnic'
        );

        if (userContrib) {
          userCommits = userContrib.total;
          userContributionPercentage = totalCommits > 0 ? Math.round((userCommits / totalCommits) * 1000) / 10 : 0;
        }
      }
    } catch (error) {
      if (options.verbose) {
        console.log(`⚠️  Contributor stats API failed for ${repoFullName}: ${error.message}`);
      }
    }

    // Method 2: If contributor stats failed, use search API to count commits
    if (userCommits === 0) {
      try {
        if (options.verbose) {
          console.log(`🔍 Using search API to count commits for ${repoFullName}...`);
        }

        // Search for commits by the user in this repository
        const { data: searchResults } = await octokit.rest.search.commits({
          q: `repo:${repoFullName} author:solnic`,
          per_page: 1
        });

        userCommits = searchResults.total_count;

        // Get total commits from repository (approximate)
        if (totalCommits === 0) {
          // Use the default branch commit count as approximation
          try {
            const { data: commits } = await octokit.rest.repos.listCommits({
              owner,
              repo,
              per_page: 1
            });
            // This is just to check if we can access commits
            // For total count, we'll use a reasonable estimate based on repo size
            totalCommits = Math.max(userCommits * 2, 100); // Conservative estimate
          } catch (e) {
            totalCommits = userCommits * 3; // Very conservative estimate
          }
        }

        if (totalCommits > 0) {
          userContributionPercentage = Math.min(Math.round((userCommits / totalCommits) * 1000) / 10, 100);
        }

      } catch (error) {
        if (options.verbose) {
          console.log(`⚠️  Search API also failed for ${repoFullName}: ${error.message}`);
        }

        // Method 3: Fallback based on ownership and known patterns
        if (owner === 'solnic') {
          userCommits = 'Creator/Maintainer';
          userContributionPercentage = 90;
        } else if (owner === 'dry-rb' || owner === 'rom-rb') {
          userCommits = 'Core Contributor';
          userContributionPercentage = 60;
        } else {
          userCommits = 'Contributor';
          userContributionPercentage = 20;
        }
      }
    }

    // Get latest commit date
    let lastCommitDate = null;
    try {
      const { data: commits } = await octokit.rest.repos.listCommits({
        owner,
        repo,
        per_page: 1
      });
      if (commits.length > 0) {
        lastCommitDate = commits[0].commit.committer.date;
      }
    } catch (error) {
      if (options.verbose) {
        console.log(`⚠️  Could not fetch latest commit for ${repoFullName}: ${error.message}`);
      }
    }

    const result = {
      name: repoData.name,
      full_name: repoData.full_name,
      description: repoData.description || '',
      url: repoData.html_url,
      language: repoData.language,
      stars: repoData.stargazers_count,
      forks: repoData.forks_count,
      size: repoData.size,
      user_commits: userCommits,
      total_commits: totalCommits,
      user_contribution_percentage: userContributionPercentage,
      last_commit_date: lastCommitDate,
      created_at: repoData.created_at,
      updated_at: repoData.updated_at,
      fetched_at: new Date().toISOString()
    };

    if (options.verbose) {
      console.log(`✅ ${repoFullName}: ${userCommits}/${totalCommits} commits (${result.user_contribution_percentage}%)`);
    }

    return result;

  } catch (error) {
    console.error(`❌ Failed to fetch data for ${repoFullName}: ${error.message}`);
    return null;
  }
};

// Test GitHub API access
const testGitHubAccess = async () => {
  try {
    const gh = initializeGitHubClient();
    const user = gh.getUser();
    const { data: userData } = await user.getProfile();

    if (options.verbose) {
      console.log(`✅ GitHub API access verified for user: ${userData.login}`);
      console.log(`📊 Rate limit remaining: ${userData.public_repos || 'Unknown'}`);
    }

    return true;
  } catch (error) {
    console.error('❌ GitHub API access test failed:', error.message);
    if (error.message.includes('401')) {
      console.error('💡 Token appears to be invalid or expired');
    } else if (error.message.includes('403')) {
      console.error('💡 Token may lack required scopes. Ensure it has "repo" or "public_repo" scope');
    }
    return false;
  }
};

// Fetch all repository contributions
const fetchAllRepositoryContributions = async () => {
  console.log('📊 Fetching repository contribution statistics...');

  // Test API access first
  const accessOk = await testGitHubAccess();
  if (!accessOk) {
    console.error('❌ Cannot proceed without valid GitHub API access');
    return {};
  }

  const repos = await getRepositoriesToAnalyze();
  console.log(`🔍 Found ${repos.length} repositories to analyze:`, repos.slice(0, 10).join(', ') + (repos.length > 10 ? '...' : ''));

  const results = {};

  for (const repo of repos) {
    const data = await fetchRepositoryContributions(repo);
    if (data) {
      results[repo] = data;
    }

    // Add delay to avoid rate limiting (longer delay for more repos)
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // Save to cache file
  const outputPath = path.resolve(options.reposOutput);
  const output = {
    generated_at: new Date().toISOString(),
    repositories: results
  };

  fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));
  console.log(`✅ Repository contribution data saved to ${outputPath}`);
  console.log(`📊 Analyzed ${Object.keys(results).length} repositories`);

  return results;
};

// Main function
const main = async () => {
  try {
    // Handle stats command
    if (options.stats) {
      await fetchContributionStats();
      return;
    }

    // Handle repos command
    if (options.repos) {
      await fetchAllRepositoryContributions();
      return;
    }

    console.log('🚀 Starting GitHub sponsors data processing...');

    // Check environment and get input path
    const { inputPath } = checkEnvironment();

    // Initialize GitHub API client
    const gh = initializeGitHubClient();

    // Validate API access
    await validateAPIAccess(gh);

    // Read exported sponsors data
    const rawSponsorsData = readSponsorsData(inputPath);

    // Process sponsors data and fetch GitHub profiles
    const processedSponsors = await processSponsorsData(gh, rawSponsorsData);

    // Format the data for CSV
    const formattedData = formatSponsorDataForCSV(processedSponsors);

    // Generate CSV and JSON content
    const csvContent = generateCSV(formattedData);
    const jsonContent = options.json ? generateJSON(processedSponsors) : null;

    // Count active vs inactive
    const activeCount = processedSponsors.filter(s => s.is_active).length;
    const inactiveCount = processedSponsors.filter(s => !s.is_active).length;

    // Prepare output paths
    const csvOutputPath = path.resolve(options.output);
    const jsonOutputPath = options.json ? csvOutputPath.replace('.csv', '.json') : null;

    if (options.dryRun) {
      console.log('\n🔍 Dry run - would write files:');
      console.log(`📊 Total sponsors: ${processedSponsors.length}`);
      console.log(`📊 Active sponsors: ${activeCount}`);
      console.log(`📊 Past sponsors: ${inactiveCount}`);
      console.log(`📄 CSV output file: ${csvOutputPath}`);
      if (jsonOutputPath) {
        console.log(`📄 JSON output file: ${jsonOutputPath}`);
      }
      console.log('\n🔍 CSV preview (first 5 lines):');
      console.log(csvContent.split('\n').slice(0, 5).join('\n'));
      console.log('\n🔍 Dry run completed - no files were written');
      return;
    }

    // Write the CSV file
    fs.writeFileSync(csvOutputPath, csvContent);
    console.log('✅ GitHub sponsors CSV updated successfully!');

    // Write the JSON file if requested
    if (options.json && jsonContent && jsonOutputPath) {
      fs.writeFileSync(jsonOutputPath, jsonContent);
      console.log('✅ GitHub sponsors JSON generated successfully!');
    }

    console.log(`📊 Total sponsors: ${processedSponsors.length}`);
    console.log(`📊 Active sponsors: ${activeCount}`);
    console.log(`📊 Past sponsors: ${inactiveCount}`);
    console.log(`💾 CSV saved to: ${csvOutputPath}`);
    if (jsonOutputPath) {
      console.log(`💾 JSON saved to: ${jsonOutputPath}`);
    }

    if (options.verbose) {
      console.log('\n📋 Next steps:');
      console.log('1. Review the generated files');
      console.log('2. Run: node scripts/ghost.js --page github-sponsors');
      console.log('3. Your sponsors page will be updated with the latest data');
      console.log('\n💡 To update sponsors data in the future:');
      console.log('   - Export fresh data from GitHub Sponsors dashboard');
      console.log('   - Replace the JSON file and run this script again');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
