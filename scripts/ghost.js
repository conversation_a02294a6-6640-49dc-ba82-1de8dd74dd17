#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { program } = require('commander');
const GhostAdminAPI = require('@tryghost/admin-api');
const TemplateEngine = require('../lib/template-engine');
const FormData = require('form-data');
const fetch = require('node-fetch');
const { Octokit } = require('@octokit/rest');

// Command line argument parsing
program
  .name('ghost')
  .description('Sync content with Ghost.io using Admin API')
  .option('--config <file>', 'Ghost config file path', 'ghost.yml')
  .option('--page <name>', 'Update specific page (github-sponsors, open-source)')
  .option('--dry-run', 'Show what would be updated without making changes')
  .option('--verbose', 'Show detailed output')
  .addHelpText('after', `
Examples:
  Update GitHub sponsors page:
    $ node scripts/ghost.js --page github-sponsors

  Update open source portfolio:
    $ node scripts/ghost.js --page open-source

  Dry run to see what would be updated:
    $ node scripts/ghost.js --page open-source --dry-run

  Generate HTML fallback if API fails:
    $ node scripts/ghost.js --page open-source --fallback
`)
  .parse();

const options = program.opts();

// Initialize template engine
const templateEngine = new TemplateEngine(path.join(__dirname, '..', 'templates'));

// Initialize GitHub API client
let octokit = null;
const initGitHubAPI = () => {
  if (!octokit) {
    // Try to get GitHub token from environment or config
    const githubToken = process.env.GITHUB_TOKEN || process.env.GH_TOKEN;
    octokit = new Octokit({
      auth: githubToken,
      userAgent: 'solnic.dev-portfolio-generator'
    });
  }
  return octokit;
};

// Load cached repository contribution data
const loadRepositoryContributions = () => {
  const repoContribPath = path.join(__dirname, '..', 'data', 'github-repo-contributions.json');

  if (!fs.existsSync(repoContribPath)) {
    if (options.verbose) {
      console.log('⚠️  Repository contributions cache not found. Run: node scripts/gh.js --repos');
    }
    return {};
  }

  try {
    const data = JSON.parse(fs.readFileSync(repoContribPath, 'utf8'));
    return data.repositories || {};
  } catch (error) {
    console.error(`❌ Failed to load repository contributions: ${error.message}`);
    return {};
  }
};

// Get repository data from cache
const getRepositoryData = (repoFullName, repoContributions) => {
  const repoData = repoContributions[repoFullName];

  if (!repoData) {
    if (options.verbose) {
      console.log(`⚠️  No cached data for ${repoFullName}. Run: node scripts/gh.js --repos`);
    }
    return null;
  }

  // Format commit information
  let commitInfo = 'Contributor';
  if (typeof repoData.user_commits === 'string') {
    commitInfo = repoData.user_commits;
  } else if (repoData.user_commits > 0) {
    commitInfo = `${repoData.user_commits} commits`;
    if (repoData.user_contribution_percentage > 0) {
      commitInfo += ` (${repoData.user_contribution_percentage}%)`;
    }
  }

  return {
    name: repoData.name,
    fullName: repoData.full_name,
    githubUrl: repoData.url,
    description: repoData.description || 'No description available',
    commits: commitInfo,
    stars: repoData.stars,
    language: repoData.language
  };
};

// Load Ghost configuration
const loadGhostConfig = () => {
  const configPath = path.resolve(options.config);

  if (!fs.existsSync(configPath)) {
    console.error(`❌ Ghost config file not found: ${configPath}`);
    process.exit(1);
  }

  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = yaml.load(configContent);

    if (!config.integrations || !config.integrations.page_sync) {
      console.error('❌ Missing page_sync integration in Ghost config');
      console.error('💡 Add integration config to ghost.yml:');
      console.error('integrations:');
      console.error('  page_sync:');
      console.error('    admin_api_key: "your_admin_api_key"');
      process.exit(1);
    }

    const integration = config.integrations.page_sync;

    if (!integration.admin_api_key) {
      console.error('❌ Missing admin_api_key in Ghost config');
      console.error('💡 Get your Admin API key from Ghost Admin → Settings → Integrations');
      process.exit(1);
    }

    return integration;
  } catch (error) {
    console.error(`❌ Error loading Ghost config: ${error.message}`);
    process.exit(1);
  }
};

// Parse CSV data
const parseCSV = (csvPath) => {
  const content = fs.readFileSync(csvPath, 'utf8');
  const lines = content.trim().split('\n');
  const headers = lines[0].split(',');

  return lines.slice(1).map(line => {
    const values = line.split(',');
    const row = {};
    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });
    return row;
  });
};

// Generate GitHub profile URL
const githubProfileUrl = (handle) => {
  return `https://github.com/${handle}`;
};

// Helper function to get icon for link type
const getIconForLinkType = (type) => {
  const icons = {
    github: 'GitHub',
    website: 'Website',
    blog_post: 'Blog Post',
    ruby_feature_request: 'Ruby Feature'
  };
  return icons[type] || 'Link';
};

// Cache for uploaded image URLs to avoid re-uploading
const uploadedImageCache = new Map();

// Upload image to Ghost CMS and return the URL
const uploadImageToGhost = async (imagePath, config) => {
  const filename = path.basename(imagePath);

  // Check cache first
  if (uploadedImageCache.has(filename)) {
    if (options.verbose) {
      console.log(`📋 Using cached URL for ${filename}`);
    }
    return uploadedImageCache.get(filename);
  }

  try {
    const imageBuffer = fs.readFileSync(imagePath);

    const form = new FormData();
    form.append('file', imageBuffer, {
      filename,
      contentType: 'image/png'
    });
    form.append('purpose', 'image');
    form.append('ref', `open-source/logos/${filename}`);

    // Extract the key parts for the JWT token
    const [id, secret] = config.admin_api_key.split(':');

    // Create JWT token manually (simplified version)
    const jwt = require('jsonwebtoken');
    const token = jwt.sign({}, Buffer.from(secret, 'hex'), {
      keyid: id,
      algorithm: 'HS256',
      expiresIn: '5m',
      audience: '/admin/'
    });

    const response = await fetch('https://solnic.ghost.io/ghost/api/admin/images/upload/', {
      method: 'POST',
      headers: {
        'Authorization': `Ghost ${token}`,
        'Accept-Version': 'v5.0',
        ...form.getHeaders()
      },
      body: form
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    const imageUrl = result.images[0].url;

    // Cache the result
    uploadedImageCache.set(filename, imageUrl);

    console.log(`✅ Uploaded ${filename} to Ghost CMS`);
    if (options.verbose) {
      console.log(`📄 URL: ${imageUrl}`);
    }
    return imageUrl;
  } catch (error) {
    console.error(`❌ Failed to upload ${imagePath}:`, error.message);
    return null;
  }
};

// Helper function to get logo URL (with Ghost CMS upload support)
const getLogoUrl = async (project, config) => {
  // Check if there's a local logo file for this project
  const localLogoPath = path.join(__dirname, '..', 'assets', 'images', `${project.id}.png`);

  if (fs.existsSync(localLogoPath)) {
    // Upload to Ghost CMS and return the Ghost URL
    const ghostUrl = await uploadImageToGhost(localLogoPath, config);
    if (ghostUrl) {
      return ghostUrl;
    }
  }

  // Fallback to external URLs for known projects
  if (project.id === 'dry-rb') {
    return 'https://dry-rb.org/assets/logo-symbol.svg';
  }
  if (project.id === 'rom-rb') {
    return 'https://rom-rb.org/assets/logo.svg';
  }
  if (project.logo) {
    return `/content/open-source/images/${project.logo}`;
  }
  return null;
};

// Generate GitHub Sponsors page HTML with embedded CSS
const generateGitHubSponsorsHTML = () => {
  const csvPath = path.join(__dirname, '..', 'data', 'github-sponsors.csv');

  if (!fs.existsSync(csvPath)) {
    console.error(`❌ GitHub sponsors CSV file not found: ${csvPath}`);
    process.exit(1);
  }

  console.log('📊 Reading GitHub sponsors data...');
  const sponsorsData = parseCSV(csvPath);

  // Process sponsors data - only include public sponsors
  const currentSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'true' && sponsor.is_public === 'true')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse()
    .map(sponsor => ({
      ...sponsor,
      name: sponsor.sponsor_name || sponsor.sponsor_handle,
      profileUrl: githubProfileUrl(sponsor.sponsor_handle)
    }));

  const pastSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'false' && sponsor.is_public === 'true')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse()
    .map(sponsor => ({
      ...sponsor,
      name: sponsor.sponsor_name || sponsor.sponsor_handle,
      profileUrl: githubProfileUrl(sponsor.sponsor_handle)
    }));

  // Prepare template data
  const templateData = {
    lastUpdated: new Date().toISOString(),
    currentSponsors,
    pastSponsors
  };

  // Render using template engine
  const html = templateEngine.renderPage('github-sponsors', templateData);

  return {
    html,
    currentSponsors: currentSponsors.length,
    pastSponsors: pastSponsors.length
  };
};

// Generate Open Source Portfolio HTML with embedded CSS for Ghost.io
const generateOpenSourcePortfolioHTML = async (config) => {
  const projectsPath = path.join(__dirname, '..', 'data', 'open-source-projects.json');
  const statsPath = path.join(__dirname, '..', 'data', 'github-stats.json');

  if (!fs.existsSync(projectsPath)) {
    console.error(`❌ Open source projects file not found: ${projectsPath}`);
    process.exit(1);
  }

  console.log('📊 Reading open source projects data...');
  const projectsData = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));

  // Try to load GitHub stats
  let statsData = null;
  if (fs.existsSync(statsPath)) {
    console.log('📈 Reading GitHub statistics...');
    statsData = JSON.parse(fs.readFileSync(statsPath, 'utf8'));
  }

  const allProjects = [...projectsData.projects, ...projectsData.past_projects];

  // Load cached repository contribution data
  const repoContributions = loadRepositoryContributions();

  // Process current projects with template data (async for logo uploads)
  const currentProjects = await Promise.all(projectsData.projects.map(async project => {
    const processedProject = {
      ...project,
      logoUrl: await getLogoUrl(project, config),
      yearRange: project.year_ended
        ? `${project.year_started} - ${project.year_ended}`
        : `${project.year_started} - Present`,
      links: Object.entries(project.links || {}).map(([key, url]) => ({
        url,
        label: getIconForLinkType(key)
      }))
    };

    // Get highlighted repositories data from cache if specified
    if (project.highlightedRepoNames && project.highlightedRepoNames.length > 0) {
      if (options.verbose) {
        console.log(`📊 Loading cached GitHub data for ${project.name}...`);
      }

      const repoDataResults = project.highlightedRepoNames.map(repoName =>
        getRepositoryData(repoName, repoContributions)
      ).filter(repo => repo !== null);

      // Add article URLs
      processedProject.highlightedRepos = repoDataResults.map(repo => ({
        ...repo,
        articleUrl: `https://solnic.dev/tags/${repo.name.replace('-', '')}/`
      }));
    }

    return processedProject;
  }));

  // Process past projects with template data (async for logo uploads)
  const pastProjects = await Promise.all(projectsData.past_projects.map(async project => {
    const processedProject = {
      ...project,
      logoUrl: await getLogoUrl(project, config),
      yearRange: project.year_ended
        ? `${project.year_started} - ${project.year_ended}`
        : `${project.year_started} - Present`,
      links: Object.entries(project.links || {}).map(([key, url]) => ({
        url,
        label: getIconForLinkType(key)
      }))
    };

    // Get highlighted repositories data from cache if specified
    if (project.highlightedRepoNames && project.highlightedRepoNames.length > 0) {
      if (options.verbose) {
        console.log(`📊 Loading cached GitHub data for ${project.name}...`);
      }

      const repoDataResults = project.highlightedRepoNames.map(repoName =>
        getRepositoryData(repoName, repoContributions)
      ).filter(repo => repo !== null);

      // Add article URLs
      processedProject.highlightedRepos = repoDataResults.map(repo => ({
        ...repo,
        articleUrl: `https://solnic.dev/tags/${repo.name.replace('-', '')}/`
      }));
    }

    return processedProject;
  }));

  // Prepare stats data
  const stats = [];

  if (statsData) {
    stats.push(
      { number: statsData.total_contributions?.toLocaleString() || '10,000+', label: 'Total Commits' },
      { number: statsData.total_repositories || '100+', label: 'Repositories' },
      { number: statsData.organizations_count || '20+', label: 'Organizations' },
      { number: '15+', label: 'Years Contributing' }
    );
  } else {
    // Fallback stats
    stats.push(
      { number: '10,000+', label: 'Total Commits' },
      { number: '100+', label: 'Repositories' },
      { number: '20+', label: 'Organizations' },
      { number: '15+', label: 'Years Contributing' }
    );
  }

  // Prepare template data
  const templateData = {
    lastUpdated: new Date().toISOString(),
    totalProjects: allProjects.length,
    stats,
    currentProjects,
    pastProjects
  };

  // Render using template engine
  const html = templateEngine.renderPage('open-source', templateData);



  return {
    html,
    totalProjects: allProjects.length,
    currentProjects: currentProjects.length,
    pastProjects: pastProjects.length
  };
};

// Initialize Ghost Admin API
const initGhostAPI = (config) => {
  if (options.verbose) {
    console.log('🔧 API Configuration:');
    console.log(`   URL: https://solnic.ghost.io`);
    console.log(`   Key: ${config.admin_api_key.substring(0, 20)}...`);
    console.log(`   Version: v5.0`);
  }

  // Validate API key format (should be id:secret)
  if (!config.admin_api_key.includes(':')) {
    throw new Error('Invalid Admin API key format. Expected format: id:secret');
  }

  return new GhostAdminAPI({
    url: 'https://solnic.ghost.io',
    key: config.admin_api_key,
    version: 'v5.0'
  });
};

// Find page by slug
const findPageBySlug = async (api, slug) => {
  try {
    const pages = await api.pages.browse({
      filter: `slug:${slug}`,
      limit: 1
    });

    return pages.length > 0 ? pages[0] : null;
  } catch (error) {
    console.error(`❌ Error finding page with slug "${slug}":`, error.message);
    throw error;
  }
};

// Update Open Source Portfolio page
const updateOpenSourcePage = async (api) => {
  console.log('🔍 Looking for open source page...');

  // Find the existing page
  const existingPage = await findPageBySlug(api, 'open-source');

  if (!existingPage) {
    console.error('❌ Open source page not found. Please create it first in Ghost admin.');
    console.error('💡 Create a new page with slug "open-source" in Ghost Admin');
    process.exit(1);
  }

  console.log(`✅ Found page: "${existingPage.title}" (ID: ${existingPage.id})`);

  // Generate new content
  const config = loadGhostConfig();
  const { html, totalProjects, currentProjects, pastProjects } = await generateOpenSourcePortfolioHTML(config);

  if (options.dryRun) {
    console.log('\n🔍 Dry run - would update page with:');
    console.log(`📊 Total projects: ${totalProjects}`);
    console.log(`📊 Current projects: ${currentProjects}`);
    console.log(`📊 Past projects: ${pastProjects}`);
    console.log(`📄 HTML length: ${html.length} characters`);
    console.log('\n🔍 HTML preview (first 300 characters):');
    console.log(html.substring(0, 300) + '...');
    console.log('\n🔍 Dry run completed - no changes were made');
    return;
  }

  try {
    // Fetch the page again to get the latest updated_at timestamp
    console.log('🔄 Fetching latest page data for update...');
    const latestPage = await findPageBySlug(api, 'open-source');

    if (!latestPage) {
      throw new Error('Page not found when trying to update');
    }

    if (options.verbose) {
      console.log(`📄 Current page updated_at: ${latestPage.updated_at}`);
      console.log(`📄 HTML content length: ${html.length} characters`);
      console.log(`📄 Page status: ${latestPage.status}`);
      console.log(`📄 Page visibility: ${latestPage.visibility}`);
    }

    // Create lexical document with HTML card (as per Ghost forum guidance)
    const lexicalContent = {
      root: {
        children: [{ type: 'html', version: 1, html: html }],
        direction: null,
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    };

    const updateData = {
      id: latestPage.id,
      title: latestPage.title, // Keep the same title
      mobiledoc: null, // Clear mobiledoc to avoid conflicts
      lexical: JSON.stringify(lexicalContent),
      updated_at: latestPage.updated_at // Required for conflict detection
    };

    if (options.verbose) {
      console.log(`📄 Update data keys: ${Object.keys(updateData).join(', ')}`);
      console.log(`📄 Lexical content type: HTML card`);
    }

    // Update the page with lexical HTML card
    const updatedPage = await api.pages.edit(updateData);

    console.log('✅ Open source portfolio page updated successfully!');
    console.log(`📊 Total projects: ${totalProjects}`);
    console.log(`📊 Current projects: ${currentProjects}`);
    console.log(`📊 Past projects: ${pastProjects}`);
    console.log(`🔗 Page URL: https://solnic.dev/${updatedPage.slug}/`);

    if (options.verbose) {
      console.log(`📄 Updated page ID: ${updatedPage.id}`);
      console.log(`📄 New updated_at: ${updatedPage.updated_at}`);
    }

  } catch (error) {
    console.error('❌ Error updating page:', error.message);

    if (error.message.includes('UpdateCollisionError')) {
      console.error('💡 The page was modified by someone else. Please try again.');
    } else if (error.message.includes('Authorization')) {
      console.error('💡 API authorization failed. Check your Admin API key permissions.');
      console.error('💡 Make sure the integration has "Pages" permissions in Ghost Admin.');
    }

    throw error;
  }
};

// Update GitHub sponsors page
const updateGitHubSponsorsPage = async (api) => {
  console.log('🔍 Looking for GitHub sponsors page...');

  // Find the existing page
  const existingPage = await findPageBySlug(api, 'github-sponsors');

  if (!existingPage) {
    console.error('❌ GitHub sponsors page not found. Please create it first in Ghost admin.');
    console.error('💡 Create a new page with slug "github-sponsors" in Ghost Admin');
    process.exit(1);
  }

  console.log(`✅ Found page: "${existingPage.title}" (ID: ${existingPage.id})`);

  // Generate new content
  const { html, currentSponsors, pastSponsors } = generateGitHubSponsorsHTML();

  if (options.dryRun) {
    console.log('\n🔍 Dry run - would update page with:');
    console.log(`📊 Current sponsors: ${currentSponsors}`);
    console.log(`📊 Past sponsors: ${pastSponsors}`);
    console.log(`📄 HTML length: ${html.length} characters`);
    console.log('\n🔍 HTML preview (first 300 characters):');
    console.log(html.substring(0, 300) + '...');
    console.log('\n🔍 Dry run completed - no changes were made');
    return;
  }

  try {
    // Fetch the page again to get the latest updated_at timestamp
    console.log('🔄 Fetching latest page data for update...');
    const latestPage = await findPageBySlug(api, 'github-sponsors');

    if (!latestPage) {
      throw new Error('Page not found when trying to update');
    }

    if (options.verbose) {
      console.log(`📄 Current page updated_at: ${latestPage.updated_at}`);
      console.log(`📄 HTML content length: ${html.length} characters`);
      console.log(`📄 Page status: ${latestPage.status}`);
      console.log(`📄 Page visibility: ${latestPage.visibility}`);
    }

    // Create lexical document with HTML card (as per Ghost forum guidance)
    const lexicalContent = {
      root: {
        children: [{ type: 'html', version: 1, html: html }],
        direction: null,
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    };

    const updateData = {
      id: latestPage.id,
      title: latestPage.title, // Keep the same title
      mobiledoc: null, // Clear mobiledoc to avoid conflicts
      lexical: JSON.stringify(lexicalContent),
      updated_at: latestPage.updated_at // Required for conflict detection
    };

    if (options.verbose) {
      console.log(`📄 Update data keys: ${Object.keys(updateData).join(', ')}`);
      console.log(`📄 Lexical content type: HTML card`);
    }

    // Update the page with lexical HTML card
    const updatedPage = await api.pages.edit(updateData);

    console.log('✅ GitHub sponsors page updated successfully!');
    console.log(`📊 Current sponsors: ${currentSponsors}`);
    console.log(`📊 Past sponsors: ${pastSponsors}`);
    console.log(`🔗 Page URL: https://solnic.dev/${updatedPage.slug}/`);

    if (options.verbose) {
      console.log(`📄 Updated page ID: ${updatedPage.id}`);
      console.log(`📄 New updated_at: ${updatedPage.updated_at}`);
    }

  } catch (error) {
    console.error('❌ Error updating page:', error.message);

    if (error.message.includes('UpdateCollisionError')) {
      console.error('💡 The page was modified by someone else. Please try again.');
    } else if (error.message.includes('Authorization')) {
      console.error('💡 API authorization failed. Check your Admin API key permissions.');
      console.error('💡 Make sure the integration has "Pages" permissions in Ghost Admin.');
    }

    throw error;
  }
};

// Main function
const main = async () => {
  try {
    console.log('🚀 Starting Ghost.io sync...');

    // Load configuration
    const config = loadGhostConfig();
    console.log('✅ Ghost configuration loaded');

    // Initialize Ghost API
    const api = initGhostAPI(config);
    console.log('✅ Ghost Admin API initialized');

    // Handle specific page updates
    if (options.page) {
      if (options.page === 'github-sponsors') {
        try {
          await updateGitHubSponsorsPage(api);
        } catch (error) {
          if (error.message.includes('Authorization') || error.message.includes('401')) {
            console.error('❌ Admin API authorization failed');
            console.error('💡 Falling back to HTML generation mode...');

            // Generate HTML as fallback
            const { html, currentSponsors, pastSponsors } = generateGitHubSponsorsHTML();
            const outputFile = 'github-sponsors-manual-update.html';

            try {
              fs.writeFileSync(outputFile, html);
              console.log('✅ HTML with embedded CSS generated successfully!');
              console.log(`💾 Output saved to: ${outputFile}`);
              console.log(`📊 Current sponsors: ${currentSponsors}`);
              console.log(`📊 Past sponsors: ${pastSponsors}`);
              console.log('\n📋 Manual Update Instructions:');
              console.log('1. Copy the entire content from the generated HTML file');
              console.log('2. Go to Ghost.io Admin → Pages → GitHub Sponsors');
              console.log('3. Delete current content and add an HTML card');
              console.log('4. Paste the HTML content into the HTML card');
              console.log('5. Publish the page');
              console.log('\n💡 To fix API access:');
              console.log('💡 1. Go to https://solnic.ghost.io/ghost/#/settings/integrations');
              console.log('💡 2. Create a new Custom Integration');
              console.log('💡 3. Copy the Admin API Key');
              console.log('💡 4. Update ghost.yml with the new key');
            } catch (writeError) {
              console.error('❌ Error writing HTML file:', writeError.message);
              process.exit(1);
            }
          } else {
            throw error;
          }
        }
      } else if (options.page === 'open-source') {
        try {
          await updateOpenSourcePage(api);
        } catch (error) {
          if (error.message.includes('Authorization') || error.message.includes('401')) {
            console.error('❌ Admin API authorization failed');
            console.error('💡 Falling back to HTML generation mode...');

            // Generate HTML as fallback
            const config = loadGhostConfig();
            const { html, totalProjects, currentProjects, pastProjects } = await generateOpenSourcePortfolioHTML(config);
            const outputFile = 'open-source-manual-update.html';

            try {
              fs.writeFileSync(outputFile, html);
              console.log('✅ HTML with embedded CSS generated successfully!');
              console.log(`💾 Output saved to: ${outputFile}`);
              console.log(`📊 Total projects: ${totalProjects}`);
              console.log(`📊 Current projects: ${currentProjects}`);
              console.log(`📊 Past projects: ${pastProjects}`);
              console.log('\n📋 Manual Update Instructions:');
              console.log('1. Copy the entire content from the generated HTML file');
              console.log('2. Go to Ghost.io Admin → Pages → Open Source');
              console.log('3. Delete current content and add an HTML card');
              console.log('4. Paste the HTML content into the HTML card');
              console.log('5. Publish the page');
              console.log('\n💡 To fix API access:');
              console.log('💡 1. Go to https://solnic.ghost.io/ghost/#/settings/integrations');
              console.log('💡 2. Create a new Custom Integration');
              console.log('💡 3. Copy the Admin API Key');
              console.log('💡 4. Update ghost.yml with the new key');
            } catch (writeError) {
              console.error('❌ Error writing HTML file:', writeError.message);
              process.exit(1);
            }
          } else {
            throw error;
          }
        }
      } else {
        console.error(`❌ Unknown page: ${options.page}`);
        console.error('💡 Supported pages: github-sponsors, open-source');
        process.exit(1);
      }
      return;
    }

    // If no specific page, show help
    console.log('💡 Please specify a page to update with --page option');
    console.log('💡 Run with --help for more information');

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
