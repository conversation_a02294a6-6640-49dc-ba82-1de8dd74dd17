{"generated_at": "2025-08-11T13:55:42.452Z", "repositories": {"solnic/drops": {"name": "drops", "full_name": "solnic/drops", "description": "🛠️ Tools for working with data effectively - data contracts using types, schemas, domain validation rules, type-safe casting, and more.", "url": "https://github.com/solnic/drops", "language": "<PERSON><PERSON><PERSON>", "stars": 292, "forks": 7, "size": 592, "user_commits": 261, "total_commits": 264, "user_contribution_percentage": 98.9, "last_commit_date": "2025-08-02T12:05:26Z", "created_at": "2023-09-04T08:36:15Z", "updated_at": "2025-08-03T22:30:40Z", "fetched_at": "2025-08-11T13:48:57.177Z"}, "dry-rb/dry-validation": {"name": "dry-validation", "full_name": "dry-rb/dry-validation", "description": "Validation library with type-safe schemas and rules", "url": "https://github.com/dry-rb/dry-validation", "language": "<PERSON>", "stars": 1380, "forks": 191, "size": 2418, "user_commits": 1150, "total_commits": 2300, "user_contribution_percentage": 50, "last_commit_date": "2025-03-19T17:21:36Z", "created_at": "2015-07-22T22:05:39Z", "updated_at": "2025-08-11T08:35:32Z", "fetched_at": "2025-08-11T13:49:00.960Z"}, "dry-rb/dry-schema": {"name": "dry-schema", "full_name": "dry-rb/dry-schema", "description": "Coercion and validation for data structures", "url": "https://github.com/dry-rb/dry-schema", "language": "<PERSON>", "stars": 454, "forks": 114, "size": 2377, "user_commits": 672, "total_commits": 992, "user_contribution_percentage": 67.7, "last_commit_date": "2025-06-09T12:44:02Z", "created_at": "2017-04-17T15:01:45Z", "updated_at": "2025-08-10T16:02:52Z", "fetched_at": "2025-08-11T13:49:04.165Z"}, "dry-rb/dry-types": {"name": "dry-types", "full_name": "dry-rb/dry-types", "description": "Flexible type system for Ruby with coercions and constraints", "url": "https://github.com/dry-rb/dry-types", "language": "<PERSON>", "stars": 870, "forks": 137, "size": 2465, "user_commits": 479, "total_commits": 958, "user_contribution_percentage": 50, "last_commit_date": "2025-06-09T09:06:26Z", "created_at": "2015-09-22T09:01:34Z", "updated_at": "2025-08-10T16:02:58Z", "fetched_at": "2025-08-11T13:49:07.866Z"}, "dry-rb/dry-monads": {"name": "dry-monads", "full_name": "dry-rb/dry-monads", "description": "Useful, common monads in idiomatic Ruby", "url": "https://github.com/dry-rb/dry-monads", "language": "<PERSON>", "stars": 813, "forks": 152, "size": 1262, "user_commits": 54, "total_commits": 108, "user_contribution_percentage": 50, "last_commit_date": "2025-06-24T20:44:16Z", "created_at": "2016-04-22T10:32:15Z", "updated_at": "2025-08-10T16:02:47Z", "fetched_at": "2025-08-11T13:49:11.536Z"}, "dry-rb/dry-system": {"name": "dry-system", "full_name": "dry-rb/dry-system", "description": "Application framework with state management and built-in dependency injection support", "url": "https://github.com/dry-rb/dry-system", "language": "<PERSON>", "stars": 363, "forks": 68, "size": 1691, "user_commits": 250, "total_commits": 500, "user_contribution_percentage": 50, "last_commit_date": "2025-07-31T20:43:29Z", "created_at": "2015-12-23T10:51:36Z", "updated_at": "2025-08-10T16:02:39Z", "fetched_at": "2025-08-11T13:49:15.252Z"}, "dry-rb/dry-effects": {"name": "dry-effects", "full_name": "dry-rb/dry-effects", "description": "Algebraic effects in Ruby", "url": "https://github.com/dry-rb/dry-effects", "language": "<PERSON>", "stars": 118, "forks": 21, "size": 814, "user_commits": 53, "total_commits": 106, "user_contribution_percentage": 50, "last_commit_date": "2025-01-09T18:02:01Z", "created_at": "2019-02-14T20:23:12Z", "updated_at": "2025-08-03T21:16:56Z", "fetched_at": "2025-08-11T13:49:18.942Z"}, "dry-rb/dry-cli": {"name": "dry-cli", "full_name": "dry-rb/dry-cli", "description": "General purpose Command Line Interface (CLI) framework for Ruby", "url": "https://github.com/dry-rb/dry-cli", "language": "<PERSON>", "stars": 340, "forks": 39, "size": 651, "user_commits": 68, "total_commits": 136, "user_contribution_percentage": 50, "last_commit_date": "2025-07-29T04:48:59Z", "created_at": "2017-04-04T07:34:18Z", "updated_at": "2025-08-03T21:13:31Z", "fetched_at": "2025-08-11T13:49:22.699Z"}, "dry-rb/dry-configurable": {"name": "dry-configurable", "full_name": "dry-rb/dry-configurable", "description": "A simple mixin to make Ruby classes configurable", "url": "https://github.com/dry-rb/dry-configurable", "language": "<PERSON>", "stars": 409, "forks": 55, "size": 776, "user_commits": 144, "total_commits": 288, "user_contribution_percentage": 50, "last_commit_date": "2025-01-06T20:11:43Z", "created_at": "2015-06-10T18:46:39Z", "updated_at": "2025-08-02T01:08:11Z", "fetched_at": "2025-08-11T13:49:26.484Z"}, "dry-rb/dry-initializer": {"name": "dry-initializer", "full_name": "dry-rb/dry-initializer", "description": "DSL for building class initializer with params and options.", "url": "https://github.com/dry-rb/dry-initializer", "language": "<PERSON>", "stars": 314, "forks": 43, "size": 657, "user_commits": 75, "total_commits": 150, "user_contribution_percentage": 50, "last_commit_date": "2025-01-01T21:08:33Z", "created_at": "2016-02-19T12:08:49Z", "updated_at": "2025-07-31T00:31:43Z", "fetched_at": "2025-08-11T13:49:30.114Z"}, "dry-rb/dry-matcher": {"name": "dry-matcher", "full_name": "dry-rb/dry-matcher", "description": "Flexible, expressive pattern matching for Ruby", "url": "https://github.com/dry-rb/dry-matcher", "language": "<PERSON>", "stars": 102, "forks": 18, "size": 358, "user_commits": 61, "total_commits": 122, "user_contribution_percentage": 50, "last_commit_date": "2023-10-02T23:11:48Z", "created_at": "2015-12-02T23:55:36Z", "updated_at": "2025-07-30T18:50:15Z", "fetched_at": "2025-08-11T13:49:33.709Z"}, "dry-rb/dry-container": {"name": "dry-container", "full_name": "dry-rb/dry-container", "description": "A simple, configurable object container implemented in Ruby", "url": "https://github.com/dry-rb/dry-container", "language": "<PERSON>", "stars": 335, "forks": 38, "size": 445, "user_commits": 94, "total_commits": 188, "user_contribution_percentage": 50, "last_commit_date": "2024-01-05T05:54:05Z", "created_at": "2015-06-09T22:57:09Z", "updated_at": "2025-07-30T18:50:13Z", "fetched_at": "2025-08-11T13:49:37.317Z"}, "dry-rb/dry-monitor": {"name": "dry-monitor", "full_name": "dry-rb/dry-monitor", "description": "Monitoring and instrumentation APIs", "url": "https://github.com/dry-rb/dry-monitor", "language": "<PERSON>", "stars": 79, "forks": 21, "size": 342, "user_commits": 101, "total_commits": 202, "user_contribution_percentage": 50, "last_commit_date": "2024-01-05T05:54:12Z", "created_at": "2016-02-05T20:13:03Z", "updated_at": "2025-07-30T18:50:10Z", "fetched_at": "2025-08-11T13:49:41.011Z"}, "dry-rb/dry-transformer": {"name": "dry-transformer", "full_name": "dry-rb/dry-transformer", "description": "Data transformation toolkit", "url": "https://github.com/dry-rb/dry-transformer", "language": "<PERSON>", "stars": 76, "forks": 9, "size": 696, "user_commits": 348, "total_commits": 696, "user_contribution_percentage": 50, "last_commit_date": "2024-01-05T05:54:22Z", "created_at": "2019-08-18T08:41:13Z", "updated_at": "2025-07-30T18:50:08Z", "fetched_at": "2025-08-11T13:49:44.717Z"}, "dry-rb/dry-rails": {"name": "dry-rails", "full_name": "dry-rb/dry-rails", "description": "The official dry-rb railtie", "url": "https://github.com/dry-rb/dry-rails", "language": "<PERSON>", "stars": 267, "forks": 25, "size": 412, "user_commits": 176, "total_commits": 352, "user_contribution_percentage": 50, "last_commit_date": "2024-01-05T05:54:23Z", "created_at": "2020-01-28T08:43:24Z", "updated_at": "2025-07-30T18:50:06Z", "fetched_at": "2025-08-11T13:49:48.235Z"}, "dry-rb/dry-transaction": {"name": "dry-transaction", "full_name": "dry-rb/dry-transaction", "description": "Business transaction DSL", "url": "https://github.com/dry-rb/dry-transaction", "language": "<PERSON>", "stars": 471, "forks": 55, "size": 628, "user_commits": 55, "total_commits": 110, "user_contribution_percentage": 50, "last_commit_date": "2024-01-17T02:01:50Z", "created_at": "2015-10-26T06:02:25Z", "updated_at": "2025-07-30T18:50:04Z", "fetched_at": "2025-08-11T13:49:52.046Z"}, "dry-rb/dry-files": {"name": "dry-files", "full_name": "dry-rb/dry-files", "description": "File utilities", "url": "https://github.com/dry-rb/dry-files", "language": "<PERSON>", "stars": 40, "forks": 7, "size": 190, "user_commits": 19, "total_commits": 100, "user_contribution_percentage": 19, "last_commit_date": "2024-01-05T05:54:23Z", "created_at": "2021-04-20T09:33:01Z", "updated_at": "2025-07-30T18:50:01Z", "fetched_at": "2025-08-11T13:49:55.776Z"}, "dry-rb/dry-view": {"name": "dry-view", "full_name": "dry-rb/dry-view", "description": "Complete, standalone view rendering system that gives you everything you need to write well-factored view code.", "url": "https://github.com/dry-rb/dry-view", "language": "<PERSON>", "stars": 149, "forks": 18, "size": 797, "user_commits": 59, "total_commits": 118, "user_contribution_percentage": 50, "last_commit_date": "2024-11-20T11:35:47Z", "created_at": "2016-03-27T10:34:28Z", "updated_at": "2025-07-30T18:49:58Z", "fetched_at": "2025-08-11T13:49:59.458Z"}, "dry-rb/dry-core": {"name": "dry-core", "full_name": "dry-rb/dry-core", "description": "A toolset of small support modules used throughout the @dry-rb & @rom-rb ecosystems", "url": "https://github.com/dry-rb/dry-core", "language": "<PERSON>", "stars": 168, "forks": 35, "size": 618, "user_commits": 104, "total_commits": 208, "user_contribution_percentage": 50, "last_commit_date": "2025-01-04T19:16:34Z", "created_at": "2016-09-02T09:00:30Z", "updated_at": "2025-07-30T18:49:54Z", "fetched_at": "2025-08-11T13:50:03.156Z"}, "dry-rb/dry-logic": {"name": "dry-logic", "full_name": "dry-rb/dry-logic", "description": "Predicate logic with rule composition", "url": "https://github.com/dry-rb/dry-logic", "language": "<PERSON>", "stars": 178, "forks": 64, "size": 781, "user_commits": 276, "total_commits": 552, "user_contribution_percentage": 50, "last_commit_date": "2025-01-04T19:28:40Z", "created_at": "2016-01-11T10:02:44Z", "updated_at": "2025-07-30T18:49:52Z", "fetched_at": "2025-08-11T13:50:06.844Z"}, "dry-rb/dry-inflector": {"name": "dry-inflector", "full_name": "dry-rb/dry-inflector", "description": "Inflector for Ruby", "url": "https://github.com/dry-rb/dry-inflector", "language": "<PERSON>", "stars": 97, "forks": 14, "size": 401, "user_commits": 61, "total_commits": 122, "user_contribution_percentage": 50, "last_commit_date": "2025-01-04T14:07:04Z", "created_at": "2017-11-07T16:06:50Z", "updated_at": "2025-07-30T18:49:50Z", "fetched_at": "2025-08-11T13:50:10.486Z"}, "dry-rb/dry-events": {"name": "dry-events", "full_name": "dry-rb/dry-events", "description": "Pub/sub system", "url": "https://github.com/dry-rb/dry-events", "language": "<PERSON>", "stars": 123, "forks": 14, "size": 335, "user_commits": 69, "total_commits": 138, "user_contribution_percentage": 50, "last_commit_date": "2025-01-04T20:01:40Z", "created_at": "2017-12-15T19:15:26Z", "updated_at": "2025-07-30T18:49:47Z", "fetched_at": "2025-08-11T13:50:14.353Z"}, "dry-rb/dry-auto_inject": {"name": "dry-auto_inject", "full_name": "dry-rb/dry-auto_inject", "description": "Container-agnostic constructor injection mixin", "url": "https://github.com/dry-rb/dry-auto_inject", "language": "<PERSON>", "stars": 177, "forks": 30, "size": 543, "user_commits": 112, "total_commits": 224, "user_contribution_percentage": 50, "last_commit_date": "2025-01-07T12:57:19Z", "created_at": "2015-08-20T10:59:06Z", "updated_at": "2025-07-30T18:49:42Z", "fetched_at": "2025-08-11T13:50:18.434Z"}, "dry-rb/dry-struct": {"name": "dry-struct", "full_name": "dry-rb/dry-struct", "description": "Typed struct and value objects", "url": "https://github.com/dry-rb/dry-struct", "language": "<PERSON>", "stars": 428, "forks": 64, "size": 800, "user_commits": 72, "total_commits": 144, "user_contribution_percentage": 50, "last_commit_date": "2025-03-09T19:09:16Z", "created_at": "2016-06-30T21:37:36Z", "updated_at": "2025-07-30T18:49:36Z", "fetched_at": "2025-08-11T13:50:21.965Z"}, "dry-rb/dry-operation": {"name": "dry-operation", "full_name": "dry-rb/dry-operation", "description": "", "url": "https://github.com/dry-rb/dry-operation", "language": "<PERSON>", "stars": 43, "forks": 7, "size": 50, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2025-03-25T15:23:03Z", "created_at": "2023-09-21T12:24:00Z", "updated_at": "2025-07-30T18:49:31Z", "fetched_at": "2025-08-11T13:50:25.592Z"}, "dry-rb/dry-logger": {"name": "dry-logger", "full_name": "dry-rb/dry-logger", "description": "Logging library", "url": "https://github.com/dry-rb/dry-logger", "language": "<PERSON>", "stars": 34, "forks": 6, "size": 427, "user_commits": 188, "total_commits": 376, "user_contribution_percentage": 50, "last_commit_date": "2025-04-16T22:52:49Z", "created_at": "2019-08-08T19:44:26Z", "updated_at": "2025-07-30T18:49:29Z", "fetched_at": "2025-08-11T13:50:29.365Z"}, "dry-rb/dry-rb.org": {"name": "dry-rb.org", "full_name": "dry-rb/dry-rb.org", "description": "The official website of dry-rb", "url": "https://github.com/dry-rb/dry-rb.org", "language": "SCSS", "stars": 96, "forks": 102, "size": 99335, "user_commits": 424, "total_commits": 848, "user_contribution_percentage": 50, "last_commit_date": "2025-06-03T11:40:10Z", "created_at": "2016-02-01T22:42:07Z", "updated_at": "2025-07-30T18:49:26Z", "fetched_at": "2025-08-11T13:50:33.042Z"}, "dry-rb/dry-system-rails": {"name": "dry-system-rails", "full_name": "dry-rb/dry-system-rails", "description": ":warning: [unmaintained] this project grew into dry-rails/dry-rails :warning:", "url": "https://github.com/dry-rb/dry-system-rails", "language": "<PERSON>", "stars": 56, "forks": 14, "size": 85, "user_commits": 25, "total_commits": 100, "user_contribution_percentage": 25, "last_commit_date": "2019-10-09T02:37:34Z", "created_at": "2016-08-16T23:27:06Z", "updated_at": "2025-05-26T14:55:58Z", "fetched_at": "2025-08-11T13:50:36.758Z"}, "dry-rb/dry-system-dependency_graph": {"name": "dry-system-dependency_graph", "full_name": "dry-rb/dry-system-dependency_graph", "description": "WIP", "url": "https://github.com/dry-rb/dry-system-dependency_graph", "language": "<PERSON>", "stars": 15, "forks": 4, "size": 332, "user_commits": 1, "total_commits": 100, "user_contribution_percentage": 1, "last_commit_date": "2022-05-28T08:15:21Z", "created_at": "2019-06-04T13:56:28Z", "updated_at": "2025-04-13T15:29:54Z", "fetched_at": "2025-08-11T13:50:40.493Z"}, "dry-rb/dry-web": {"name": "dry-web", "full_name": "dry-rb/dry-web", "description": "Lightweight web application stack with pluggable routing front-ends", "url": "https://github.com/dry-rb/dry-web", "language": "<PERSON>", "stars": 212, "forks": 19, "size": 296, "user_commits": 141, "total_commits": 282, "user_contribution_percentage": 50, "last_commit_date": "2020-08-10T10:45:09Z", "created_at": "2015-01-26T22:21:40Z", "updated_at": "2025-02-26T22:28:22Z", "fetched_at": "2025-08-11T13:50:44.210Z"}, "dry-rb/devtools": {"name": "devtools", "full_name": "dry-rb/devtools", "description": "Shared tools, configuration and maintenance automation for dry-rb repos", "url": "https://github.com/dry-rb/devtools", "language": "<PERSON>", "stars": 9, "forks": 7, "size": 353, "user_commits": 270, "total_commits": 540, "user_contribution_percentage": 50, "last_commit_date": "2024-11-20T11:28:00Z", "created_at": "2019-03-08T19:54:40Z", "updated_at": "2024-11-20T11:28:08Z", "fetched_at": "2025-08-11T13:50:47.945Z"}, "dry-rb/dry-equalizer": {"name": "dry-equalizer", "full_name": "dry-rb/dry-equalizer", "description": "⚠ Dry::Equalizer is now part of dry-core; this separate codebase is now deprecated ⚠", "url": "https://github.com/dry-rb/dry-equalizer", "language": "<PERSON>", "stars": 79, "forks": 13, "size": 371, "user_commits": 71, "total_commits": 142, "user_contribution_percentage": 50, "last_commit_date": "2022-12-26T07:55:08Z", "created_at": "2015-11-11T13:02:06Z", "updated_at": "2024-09-30T22:33:58Z", "fetched_at": "2025-08-11T13:50:51.433Z"}, "dry-rb/workshop-app": {"name": "workshop-app", "full_name": "dry-rb/workshop-app", "description": "Interactive learning app for dry-rb workshops", "url": "https://github.com/dry-rb/workshop-app", "language": "<PERSON>", "stars": 15, "forks": 8, "size": 86, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2017-11-01T05:19:59Z", "created_at": "2017-07-28T10:44:22Z", "updated_at": "2024-07-10T22:57:43Z", "fetched_at": "2025-08-11T13:50:55.123Z"}, "dry-rb/dry-web-blog": {"name": "dry-web-blog", "full_name": "dry-rb/dry-web-blog", "description": "Example dry-rb & rom-rb web application", "url": "https://github.com/dry-rb/dry-web-blog", "language": "<PERSON>", "stars": 61, "forks": 11, "size": 63, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2018-06-02T11:57:44Z", "created_at": "2017-06-22T01:01:27Z", "updated_at": "2024-07-10T22:56:08Z", "fetched_at": "2025-08-11T13:50:58.800Z"}, "dry-rb/dry-web-roda": {"name": "dry-web-roda", "full_name": "dry-rb/dry-web-roda", "description": "Roda integration for dry-web apps", "url": "https://github.com/dry-rb/dry-web-roda", "language": "<PERSON>", "stars": 119, "forks": 22, "size": 477, "user_commits": 66, "total_commits": 132, "user_contribution_percentage": 50, "last_commit_date": "2021-04-06T10:33:11Z", "created_at": "2016-06-11T05:59:48Z", "updated_at": "2024-07-10T22:55:34Z", "fetched_at": "2025-08-11T13:51:02.522Z"}, "dry-rb/dry-pipeline": {"name": "dry-pipeline", "full_name": "dry-rb/dry-pipeline", "description": "The `>>` operator for Ruby aka \"pipeline operator\"", "url": "https://github.com/dry-rb/dry-pipeline", "language": "<PERSON>", "stars": 75, "forks": 2, "size": 17, "user_commits": 7, "total_commits": 100, "user_contribution_percentage": 7, "last_commit_date": "2019-01-13T10:17:22Z", "created_at": "2015-08-18T15:25:41Z", "updated_at": "2023-01-28T19:17:17Z", "fetched_at": "2025-08-11T13:51:06.161Z"}, "rom-rb/rom": {"name": "rom", "full_name": "rom-rb/rom", "description": "Data mapping and persistence toolkit for Ruby", "url": "https://github.com/rom-rb/rom", "language": "<PERSON>", "stars": 2103, "forks": 161, "size": 8685, "user_commits": 2650, "total_commits": 5300, "user_contribution_percentage": 50, "last_commit_date": "2025-01-21T13:59:26Z", "created_at": "2013-06-15T14:59:58Z", "updated_at": "2025-08-04T19:09:06Z", "fetched_at": "2025-08-11T13:51:09.704Z"}, "rom-rb/rom-rails": {"name": "rom-rails", "full_name": "rom-rb/rom-rails", "description": "Rails integration for Ruby Object Mapper", "url": "https://github.com/rom-rb/rom-rails", "language": "<PERSON>", "stars": 160, "forks": 50, "size": 543, "user_commits": 257, "total_commits": 514, "user_contribution_percentage": 50, "last_commit_date": "2023-10-29T13:26:21Z", "created_at": "2014-01-28T00:29:30Z", "updated_at": "2025-07-28T08:11:42Z", "fetched_at": "2025-08-11T13:51:13.421Z"}, "rom-rb/rom-factory": {"name": "rom-factory", "full_name": "rom-rb/rom-factory", "description": "Data generator with support for persistence backends", "url": "https://github.com/rom-rb/rom-factory", "language": "<PERSON>", "stars": 82, "forks": 44, "size": 559, "user_commits": 139, "total_commits": 278, "user_contribution_percentage": 50, "last_commit_date": "2025-01-21T11:51:24Z", "created_at": "2017-03-02T21:10:22Z", "updated_at": "2025-07-13T05:33:39Z", "fetched_at": "2025-08-11T13:51:17.106Z"}, "rom-rb/rom-rb.org": {"name": "rom-rb.org", "full_name": "rom-rb/rom-rb.org", "description": "The official rom-rb website", "url": "https://github.com/rom-rb/rom-rb.org", "language": "SCSS", "stars": 46, "forks": 106, "size": 6509, "user_commits": 472, "total_commits": 944, "user_contribution_percentage": 50, "last_commit_date": "2025-07-04T08:17:56Z", "created_at": "2013-06-02T10:50:47Z", "updated_at": "2025-07-04T08:18:01Z", "fetched_at": "2025-08-11T13:51:21.171Z"}, "rom-rb/rom-sql": {"name": "rom-sql", "full_name": "rom-rb/rom-sql", "description": "SQL support for rom-rb", "url": "https://github.com/rom-rb/rom-sql", "language": "<PERSON>", "stars": 218, "forks": 94, "size": 2884, "user_commits": 643, "total_commits": 1286, "user_contribution_percentage": 50, "last_commit_date": "2025-01-08T21:10:56Z", "created_at": "2014-11-12T19:57:49Z", "updated_at": "2025-06-28T17:43:12Z", "fetched_at": "2025-08-11T13:51:24.846Z"}, "rom-rb/rom-cassandra": {"name": "rom-cassandra", "full_name": "rom-rb/rom-cassandra", "description": "", "url": "https://github.com/rom-rb/rom-cassandra", "language": "<PERSON>", "stars": 8, "forks": 4, "size": 263, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2015-10-31T20:21:19Z", "created_at": "2015-08-25T14:50:30Z", "updated_at": "2025-05-07T23:33:44Z", "fetched_at": "2025-08-11T13:51:28.461Z"}, "rom-rb/rom-mongo": {"name": "rom-mongo", "full_name": "rom-rb/rom-mongo", "description": "(Looking for a maintainer) MongoDB support for rom-rb", "url": "https://github.com/rom-rb/rom-mongo", "language": "<PERSON>", "stars": 34, "forks": 21, "size": 78, "user_commits": 38, "total_commits": 100, "user_contribution_percentage": 38, "last_commit_date": "2017-10-19T06:17:18Z", "created_at": "2014-11-12T20:19:20Z", "updated_at": "2025-03-28T04:41:55Z", "fetched_at": "2025-08-11T13:51:32.160Z"}, "rom-rb/rom-yesql": {"name": "rom-yesql", "full_name": "rom-rb/rom-yesql", "description": "rom-rb adapter inspired by original Yesql from the Clojure world", "url": "https://github.com/rom-rb/rom-yesql", "language": "<PERSON>", "stars": 32, "forks": 5, "size": 148, "user_commits": 72, "total_commits": 144, "user_contribution_percentage": 50, "last_commit_date": "2025-01-19T14:20:40Z", "created_at": "2015-02-13T18:25:35Z", "updated_at": "2025-01-19T14:20:44Z", "fetched_at": "2025-08-11T13:51:35.926Z"}, "rom-rb/rom-migrator": {"name": "rom-migrator", "full_name": "rom-rb/rom-migrator", "description": "Database migrations for ROM adapters", "url": "https://github.com/rom-rb/rom-migrator", "language": "<PERSON>", "stars": 6, "forks": 1, "size": 272, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2015-10-18T21:33:49Z", "created_at": "2015-10-01T19:43:58Z", "updated_at": "2025-01-01T23:06:51Z", "fetched_at": "2025-08-11T13:51:39.645Z"}, "rom-rb/rom-repository": {"name": "rom-repository", "full_name": "rom-rb/rom-repository", "description": "THIS PROJECT WAS MOVED TO rom-rb/rom", "url": "https://github.com/rom-rb/rom-repository", "language": "<PERSON>", "stars": 44, "forks": 14, "size": 863, "user_commits": 239, "total_commits": 478, "user_contribution_percentage": 50, "last_commit_date": "2017-07-04T23:30:54Z", "created_at": "2015-07-21T12:22:42Z", "updated_at": "2024-12-05T11:47:47Z", "fetched_at": "2025-08-11T13:51:43.271Z"}, "rom-rb/rom-neo4j": {"name": "rom-neo4j", "full_name": "rom-rb/rom-neo4j", "description": "Experimental integration of Neo4j with Ruby Object Mapper", "url": "https://github.com/rom-rb/rom-neo4j", "language": "<PERSON>", "stars": 22, "forks": 2, "size": 375, "user_commits": 9, "total_commits": 100, "user_contribution_percentage": 9, "last_commit_date": "2015-09-09T11:36:57Z", "created_at": "2015-02-08T03:47:18Z", "updated_at": "2024-12-05T11:41:49Z", "fetched_at": "2025-08-11T13:51:46.935Z"}, "rom-rb/rom-kafka": {"name": "rom-kafka", "full_name": "rom-rb/rom-kafka", "description": "Apache Kafka support for Ruby Object Mapper", "url": "https://github.com/rom-rb/rom-kafka", "language": "<PERSON>", "stars": 13, "forks": 3, "size": 104, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2016-08-13T20:47:30Z", "created_at": "2015-09-14T22:26:27Z", "updated_at": "2024-11-28T16:31:37Z", "fetched_at": "2025-08-11T13:51:50.445Z"}, "rom-rb/rom-http": {"name": "rom-http", "full_name": "rom-rb/rom-http", "description": "Abstract HTTP adapter for ROM", "url": "https://github.com/rom-rb/rom-http", "language": "<PERSON>", "stars": 72, "forks": 18, "size": 313, "user_commits": 81, "total_commits": 162, "user_contribution_percentage": 50, "last_commit_date": "2022-12-30T13:08:59Z", "created_at": "2015-05-16T16:20:21Z", "updated_at": "2024-11-28T16:31:06Z", "fetched_at": "2025-08-11T13:51:54.104Z"}, "rom-rb/rom-git": {"name": "rom-git", "full_name": "rom-rb/rom-git", "description": "Minimal Git support for ROM (Ruby Object Mapper)", "url": "https://github.com/rom-rb/rom-git", "language": "<PERSON>", "stars": 26, "forks": 2, "size": 36, "user_commits": 1, "total_commits": 100, "user_contribution_percentage": 1, "last_commit_date": "2018-02-15T15:36:55Z", "created_at": "2015-01-08T22:38:57Z", "updated_at": "2024-08-29T23:44:58Z", "fetched_at": "2025-08-11T13:51:57.936Z"}, "rom-rb/rom-couchdb": {"name": "rom-couchdb", "full_name": "rom-rb/rom-couchdb", "description": "Experimental CouchDB support for ROM", "url": "https://github.com/rom-rb/rom-couchdb", "language": "<PERSON>", "stars": 18, "forks": 2, "size": 32, "user_commits": 1, "total_commits": 100, "user_contribution_percentage": 1, "last_commit_date": "2017-01-31T14:11:55Z", "created_at": "2015-04-22T10:04:30Z", "updated_at": "2024-08-27T08:43:55Z", "fetched_at": "2025-08-11T13:52:01.548Z"}, "rom-rb/rom-roda": {"name": "rom-roda", "full_name": "rom-rb/rom-roda", "description": "Roda plugin for Ruby Object Mapper", "url": "https://github.com/rom-rb/rom-roda", "language": "<PERSON>", "stars": 22, "forks": 3, "size": 227, "user_commits": 7, "total_commits": 100, "user_contribution_percentage": 7, "last_commit_date": "2015-10-27T11:09:50Z", "created_at": "2015-04-05T01:40:37Z", "updated_at": "2024-08-10T02:41:19Z", "fetched_at": "2025-08-11T13:52:05.240Z"}, "rom-rb/rom-event_store": {"name": "rom-event_store", "full_name": "rom-rb/rom-event_store", "description": "Event Store support for Ruby Object Mapper", "url": "https://github.com/rom-rb/rom-event_store", "language": "<PERSON>", "stars": 10, "forks": 3, "size": 50, "user_commits": 9, "total_commits": 100, "user_contribution_percentage": 9, "last_commit_date": "2016-02-10T01:17:42Z", "created_at": "2015-03-30T15:57:33Z", "updated_at": "2024-07-29T23:37:39Z", "fetched_at": "2025-08-11T13:52:08.895Z"}, "rom-rb/rom-csv": {"name": "rom-csv", "full_name": "rom-rb/rom-csv", "description": "CSV support for Ruby Object Mapper", "url": "https://github.com/rom-rb/rom-csv", "language": "<PERSON>", "stars": 23, "forks": 10, "size": 101, "user_commits": 23, "total_commits": 100, "user_contribution_percentage": 23, "last_commit_date": "2021-03-30T15:50:32Z", "created_at": "2015-01-02T22:43:06Z", "updated_at": "2024-04-17T06:47:24Z", "fetched_at": "2025-08-11T13:52:12.641Z"}, "rom-rb/rom-mapper": {"name": "rom-mapper", "full_name": "rom-rb/rom-mapper", "description": "The project was moved to rom-rb/rom", "url": "https://github.com/rom-rb/rom-mapper", "language": "<PERSON>", "stars": 62, "forks": 15, "size": 223, "user_commits": 144, "total_commits": 288, "user_contribution_percentage": 50, "last_commit_date": "2019-10-06T10:38:03Z", "created_at": "2013-06-04T17:15:18Z", "updated_at": "2024-04-16T00:11:48Z", "fetched_at": "2025-08-11T13:52:16.317Z"}, "rom-rb/rom-json": {"name": "rom-json", "full_name": "rom-rb/rom-json", "description": "JSON adapter for ROM", "url": "https://github.com/rom-rb/rom-json", "language": "<PERSON>", "stars": 16, "forks": 8, "size": 20, "user_commits": 2, "total_commits": 100, "user_contribution_percentage": 2, "last_commit_date": "2021-03-30T15:35:07Z", "created_at": "2015-08-07T09:08:59Z", "updated_at": "2024-04-10T18:16:20Z", "fetched_at": "2025-08-11T13:52:20.080Z"}, "rom-rb/api.rom-rb.org": {"name": "api.rom-rb.org", "full_name": "rom-rb/api.rom-rb.org", "description": "API doc site for all rom-rb gems", "url": "https://github.com/rom-rb/api.rom-rb.org", "language": "<PERSON>", "stars": 3, "forks": 1, "size": 7856, "user_commits": 13, "total_commits": 100, "user_contribution_percentage": 13, "last_commit_date": "2020-01-25T18:48:15Z", "created_at": "2017-11-03T02:59:37Z", "updated_at": "2024-01-17T12:08:49Z", "fetched_at": "2025-08-11T13:52:23.971Z"}, "rom-rb/rom-yaml": {"name": "rom-yaml", "full_name": "rom-rb/rom-yaml", "description": "YAML support for Ruby Object Mapper", "url": "https://github.com/rom-rb/rom-yaml", "language": "<PERSON>", "stars": 28, "forks": 8, "size": 75, "user_commits": 43, "total_commits": 100, "user_contribution_percentage": 43, "last_commit_date": "2022-12-17T07:06:48Z", "created_at": "2014-12-23T01:04:41Z", "updated_at": "2023-12-23T01:16:18Z", "fetched_at": "2025-08-11T13:52:27.594Z"}, "rom-rb/rom-redis": {"name": "rom-redis", "full_name": "rom-rb/rom-redis", "description": "Redis support for ROM (looking for a maintainer)", "url": "https://github.com/rom-rb/rom-redis", "language": "<PERSON>", "stars": 19, "forks": 12, "size": 26, "user_commits": 6, "total_commits": 100, "user_contribution_percentage": 6, "last_commit_date": "2020-03-31T11:02:43Z", "created_at": "2015-02-08T17:08:23Z", "updated_at": "2023-12-20T04:26:34Z", "fetched_at": "2025-08-11T13:52:31.321Z"}, "rom-rb/rom-elasticsearch": {"name": "rom-elasticsearch", "full_name": "rom-rb/rom-elasticsearch", "description": "Elasticsearch adapter for rom-rb", "url": "https://github.com/rom-rb/rom-elasticsearch", "language": "<PERSON>", "stars": 34, "forks": 15, "size": 260, "user_commits": 70, "total_commits": 140, "user_contribution_percentage": 50, "last_commit_date": "2022-04-03T12:10:07Z", "created_at": "2015-06-01T13:15:51Z", "updated_at": "2023-03-14T13:59:40Z", "fetched_at": "2025-08-11T13:52:35.004Z"}, "rom-rb/rom-support": {"name": "rom-support", "full_name": "rom-rb/rom-support", "description": "DISCOUNTINUED (see README)", "url": "https://github.com/rom-rb/rom-support", "language": "<PERSON>", "stars": 4, "forks": 6, "size": 251, "user_commits": 281, "total_commits": 562, "user_contribution_percentage": 50, "last_commit_date": "2017-01-05T15:46:19Z", "created_at": "2015-08-09T19:12:55Z", "updated_at": "2022-07-05T10:33:56Z", "fetched_at": "2025-08-11T13:52:38.866Z"}, "rom-rb/rom-influxdb": {"name": "rom-influxdb", "full_name": "rom-rb/rom-influxdb", "description": "InfluxDB Support for Ruby Object Mapper", "url": "https://github.com/rom-rb/rom-influxdb", "language": "<PERSON>", "stars": 8, "forks": 3, "size": 170, "user_commits": 2, "total_commits": 100, "user_contribution_percentage": 2, "last_commit_date": "2015-09-26T22:23:51Z", "created_at": "2015-03-10T17:30:38Z", "updated_at": "2022-07-05T10:32:07Z", "fetched_at": "2025-08-11T13:52:42.616Z"}, "rom-rb/rom-rethinkdb": {"name": "rom-rethinkdb", "full_name": "rom-rb/rom-rethinkdb", "description": "RethinkDB support for ROM", "url": "https://github.com/rom-rb/rom-rethinkdb", "language": "<PERSON>", "stars": 14, "forks": 10, "size": 31, "user_commits": 16, "total_commits": 100, "user_contribution_percentage": 16, "last_commit_date": "2017-04-13T21:36:29Z", "created_at": "2015-04-30T21:16:27Z", "updated_at": "2022-01-29T20:39:31Z", "fetched_at": "2025-08-11T13:52:46.175Z"}, "rom-rb/rom-dynamodb": {"name": "rom-dynamodb", "full_name": "rom-rb/rom-dynamodb", "description": "ROM DynamoDB Adapter", "url": "https://github.com/rom-rb/rom-dynamodb", "language": "<PERSON>", "stars": 3, "forks": 1, "size": 51, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2017-02-01T15:04:44Z", "created_at": "2017-12-23T09:15:02Z", "updated_at": "2020-02-23T22:46:49Z", "fetched_at": "2025-08-11T13:52:49.858Z"}, "rom-rb/rom-model": {"name": "rom-model", "full_name": "rom-rb/rom-model", "description": "A small collection of extensions useful for data coercion and validation", "url": "https://github.com/rom-rb/rom-model", "language": "<PERSON>", "stars": 15, "forks": 4, "size": 79, "user_commits": 81, "total_commits": 162, "user_contribution_percentage": 50, "last_commit_date": "2017-07-10T22:07:25Z", "created_at": "2015-08-11T20:08:04Z", "updated_at": "2019-04-03T22:55:16Z", "fetched_at": "2025-08-11T13:52:53.458Z"}, "rom-rb/rom-bench": {"name": "rom-bench", "full_name": "rom-rb/rom-bench", "description": "Bench and profiling setup for rom-rb devel", "url": "https://github.com/rom-rb/rom-bench", "language": null, "stars": 0, "forks": 0, "size": 0, "user_commits": 0, "total_commits": 0, "user_contribution_percentage": 0, "last_commit_date": null, "created_at": "2017-01-27T15:03:40Z", "updated_at": "2017-01-27T15:03:40Z", "fetched_at": "2025-08-11T13:52:56.984Z"}, "hanami/hanami": {"name": "hanami", "full_name": "hanami/hanami", "description": "A flexible framework for maintainable Ruby apps", "url": "https://github.com/hanami/hanami", "language": "<PERSON>", "stars": 6285, "forks": 542, "size": 26825, "user_commits": 63, "total_commits": 126, "user_contribution_percentage": 50, "last_commit_date": "2025-08-01T06:46:51Z", "created_at": "2013-08-09T15:24:44Z", "updated_at": "2025-08-11T11:32:02Z", "fetched_at": "2025-08-11T13:53:00.544Z"}, "hanami/cli": {"name": "cli", "full_name": "hanami/cli", "description": "Hanami command line", "url": "https://github.com/hanami/cli", "language": "<PERSON>", "stars": 28, "forks": 38, "size": 857, "user_commits": 36, "total_commits": 100, "user_contribution_percentage": 36, "last_commit_date": "2025-08-08T18:56:31Z", "created_at": "2021-03-11T15:15:52Z", "updated_at": "2025-08-08T18:56:35Z", "fetched_at": "2025-08-11T13:53:04.256Z"}, "hanami/controller": {"name": "controller", "full_name": "hanami/controller", "description": "Complete, fast and testable actions for <PERSON><PERSON> and <PERSON><PERSON>", "url": "https://github.com/hanami/controller", "language": "<PERSON>", "stars": 243, "forks": 111, "size": 1703, "user_commits": 9, "total_commits": 100, "user_contribution_percentage": 9, "last_commit_date": "2025-08-01T05:18:33Z", "created_at": "2013-06-25T08:20:55Z", "updated_at": "2025-08-01T05:18:37Z", "fetched_at": "2025-08-11T13:53:07.968Z"}, "hanami/router": {"name": "router", "full_name": "hanami/router", "description": "Ruby/Rack HTTP router", "url": "https://github.com/hanami/router", "language": "<PERSON>", "stars": 360, "forks": 94, "size": 1033, "user_commits": 5, "total_commits": 100, "user_contribution_percentage": 5, "last_commit_date": "2025-08-01T05:16:24Z", "created_at": "2013-06-14T20:07:26Z", "updated_at": "2025-08-01T05:16:29Z", "fetched_at": "2025-08-11T13:53:11.794Z"}, "hanami/devtools": {"name": "devtools", "full_name": "hanami/devtools", "description": "Hanami development tools [internal usage]", "url": "https://github.com/hanami/devtools", "language": "<PERSON>", "stars": 4, "forks": 6, "size": 135, "user_commits": 1, "total_commits": 100, "user_contribution_percentage": 1, "last_commit_date": "2025-08-01T05:01:00Z", "created_at": "2017-10-03T07:47:21Z", "updated_at": "2025-08-01T05:01:05Z", "fetched_at": "2025-08-11T13:53:15.430Z"}, "hanami/guides": {"name": "guides", "full_name": "hanami/guides", "description": "Hanami Guides", "url": "https://github.com/hanami/guides", "language": "CSS", "stars": 55, "forks": 93, "size": 26295, "user_commits": 18, "total_commits": 100, "user_contribution_percentage": 18, "last_commit_date": "2025-07-31T12:34:20Z", "created_at": "2018-10-15T16:12:43Z", "updated_at": "2025-07-31T12:34:25Z", "fetched_at": "2025-08-11T13:53:19.113Z"}, "hanami/api": {"name": "api", "full_name": "hanami/api", "description": "Minimal, lightweight, fastest Ruby framework for HTTP APIs.", "url": "https://github.com/hanami/api", "language": "<PERSON>", "stars": 356, "forks": 13, "size": 91, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2025-07-09T21:33:00Z", "created_at": "2020-02-08T15:36:43Z", "updated_at": "2025-07-31T07:08:27Z", "fetched_at": "2025-08-11T13:53:22.788Z"}, "hanami/utils": {"name": "utils", "full_name": "hanami/utils", "description": "Ruby core extentions and class utilities for Hanami", "url": "https://github.com/hanami/utils", "language": "<PERSON>", "stars": 173, "forks": 102, "size": 1387, "user_commits": 5, "total_commits": 100, "user_contribution_percentage": 5, "last_commit_date": "2025-07-27T12:36:47Z", "created_at": "2013-08-07T12:29:45Z", "updated_at": "2025-07-27T12:36:54Z", "fetched_at": "2025-08-11T13:53:26.417Z"}, "hanami/model": {"name": "model", "full_name": "hanami/model", "description": "Ruby persistence framework with entities and repositories", "url": "https://github.com/hanami/model", "language": "<PERSON>", "stars": 443, "forks": 150, "size": 1812, "user_commits": 1, "total_commits": 100, "user_contribution_percentage": 1, "last_commit_date": "2024-07-03T21:56:40Z", "created_at": "2014-02-12T08:25:42Z", "updated_at": "2025-07-27T09:59:38Z", "fetched_at": "2025-08-11T13:53:30.525Z"}, "hanami/hanami.github.io": {"name": "hanami.github.io", "full_name": "hanami/hanami.github.io", "description": "Hanami website", "url": "https://github.com/hanami/hanami.github.io", "language": "HTML", "stars": 88, "forks": 170, "size": 59965, "user_commits": 2, "total_commits": 100, "user_contribution_percentage": 2, "last_commit_date": "2025-07-24T14:05:43Z", "created_at": "2014-01-01T11:16:40Z", "updated_at": "2025-07-24T14:05:50Z", "fetched_at": "2025-08-11T13:53:34.302Z"}, "hanami/view": {"name": "view", "full_name": "hanami/view", "description": "Views, templates and presenters for Ruby web applications", "url": "https://github.com/hanami/view", "language": "<PERSON>", "stars": 175, "forks": 78, "size": 1496, "user_commits": 53, "total_commits": 106, "user_contribution_percentage": 50, "last_commit_date": "2025-07-02T20:32:01Z", "created_at": "2013-07-15T09:42:16Z", "updated_at": "2025-07-14T09:45:47Z", "fetched_at": "2025-08-11T13:53:37.973Z"}, "hanami/db": {"name": "db", "full_name": "hanami/db", "description": "The database layer for Hanami", "url": "https://github.com/hanami/db", "language": "<PERSON>", "stars": 11, "forks": 3, "size": 32, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2025-05-06T22:42:03Z", "created_at": "2024-05-20T05:54:32Z", "updated_at": "2025-06-23T18:19:52Z", "fetched_at": "2025-08-11T13:53:41.582Z"}, "hanami/events": {"name": "events", "full_name": "hanami/events", "description": "[Experimental] Events framework for Hanami", "url": "https://github.com/hanami/events", "language": "<PERSON>", "stars": 42, "forks": 7, "size": 136, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2020-12-15T17:08:40Z", "created_at": "2017-07-31T07:59:41Z", "updated_at": "2025-06-05T02:11:43Z", "fetched_at": "2025-08-11T13:53:45.175Z"}, "hanami/assets-js": {"name": "assets-js", "full_name": "hanami/assets-js", "description": "esbuild plugin for Hanami Assets", "url": "https://github.com/hanami/assets-js", "language": "TypeScript", "stars": 4, "forks": 6, "size": 1047, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2025-03-27T08:52:37Z", "created_at": "2023-03-20T09:27:20Z", "updated_at": "2025-03-27T08:52:42Z", "fetched_at": "2025-08-11T13:53:48.892Z"}, "hanami/assets": {"name": "assets", "full_name": "hanami/assets", "description": "Assets management for Ruby web applications", "url": "https://github.com/hanami/assets", "language": "<PERSON>", "stars": 47, "forks": 41, "size": 1042, "user_commits": 1, "total_commits": 100, "user_contribution_percentage": 1, "last_commit_date": "2025-03-09T01:00:12Z", "created_at": "2014-05-13T16:28:38Z", "updated_at": "2025-03-09T01:00:18Z", "fetched_at": "2025-08-11T13:53:52.552Z"}, "hanami/validations": {"name": "validations", "full_name": "hanami/validations", "description": "Validation mixin for Ruby objects", "url": "https://github.com/hanami/validations", "language": "<PERSON>", "stars": 208, "forks": 45, "size": 827, "user_commits": 7, "total_commits": 100, "user_contribution_percentage": 7, "last_commit_date": "2025-01-01T03:35:39Z", "created_at": "2014-08-05T12:49:35Z", "updated_at": "2025-03-01T16:21:06Z", "fetched_at": "2025-08-11T13:53:56.251Z"}, "hanami/hanami-2-application-template": {"name": "hanami-2-application-template", "full_name": "hanami/hanami-2-application-template", "description": "Hanami 2 application starter template", "url": "https://github.com/hanami/hanami-2-application-template", "language": "<PERSON>", "stars": 108, "forks": 24, "size": 297, "user_commits": 45, "total_commits": 100, "user_contribution_percentage": 45, "last_commit_date": "2022-11-08T12:15:45Z", "created_at": "2020-05-03T23:31:17Z", "updated_at": "2025-01-31T15:50:23Z", "fetched_at": "2025-08-11T13:53:59.796Z"}, "hanami/reloader": {"name": "reloader", "full_name": "hanami/reloader", "description": "Code reloading for Hanami 2", "url": "https://github.com/hanami/reloader", "language": "<PERSON>", "stars": 14, "forks": 5, "size": 103, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2025-01-01T03:41:21Z", "created_at": "2017-09-27T12:56:03Z", "updated_at": "2025-01-01T03:41:24Z", "fetched_at": "2025-08-11T13:54:03.370Z"}, "hanami/helpers": {"name": "helpers", "full_name": "hanami/helpers", "description": "View helpers for Ruby applications", "url": "https://github.com/hanami/helpers", "language": "<PERSON>", "stars": 77, "forks": 50, "size": 683, "user_commits": 2, "total_commits": 100, "user_contribution_percentage": 2, "last_commit_date": "2022-12-31T11:34:39Z", "created_at": "2014-05-13T16:26:00Z", "updated_at": "2024-12-10T10:08:22Z", "fetched_at": "2025-08-11T13:54:07.261Z"}, "hanami/ecosystem": {"name": "ecosystem", "full_name": "hanami/ecosystem", "description": "Hanami Ecosystem", "url": "https://github.com/hanami/ecosystem", "language": null, "stars": 11, "forks": 3, "size": 3, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2016-12-21T21:26:55Z", "created_at": "2016-12-01T15:57:54Z", "updated_at": "2024-12-05T11:47:49Z", "fetched_at": "2025-08-11T13:54:10.772Z"}, "hanami/docs": {"name": "docs", "full_name": "hanami/docs", "description": "Hanami API Docs", "url": "https://github.com/hanami/docs", "language": "CSS", "stars": 4, "forks": 6, "size": 663, "user_commits": 1, "total_commits": 100, "user_contribution_percentage": 1, "last_commit_date": "2024-08-02T18:34:58Z", "created_at": "2017-03-08T09:09:00Z", "updated_at": "2024-08-02T18:35:04Z", "fetched_at": "2025-08-11T13:54:14.463Z"}, "hanami/mailer": {"name": "mailer", "full_name": "hanami/mailer", "description": "Mail for Ruby applications", "url": "https://github.com/hanami/mailer", "language": "<PERSON>", "stars": 48, "forks": 21, "size": 336, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2023-07-12T10:50:34Z", "created_at": "2015-06-25T07:21:02Z", "updated_at": "2023-09-18T21:15:09Z", "fetched_at": "2025-08-11T13:54:18.154Z"}, "hanami/snippets": {"name": "snippets", "full_name": "hanami/snippets", "description": "Learn <PERSON> by reading, short, curated, manually crafted code snippets.", "url": "https://github.com/hanami/snippets", "language": "CSS", "stars": 10, "forks": 2, "size": 11656, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2019-03-03T09:49:51Z", "created_at": "2018-09-27T12:11:49Z", "updated_at": "2023-09-08T17:45:30Z", "fetched_at": "2025-08-11T13:54:21.847Z"}, "hanami/bookshelf": {"name": "bookshelf", "full_name": "hanami/bookshelf", "description": "<PERSON><PERSON> \"Getting Started\" project", "url": "https://github.com/hanami/bookshelf", "language": "<PERSON>", "stars": 21, "forks": 13, "size": 139, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2021-06-30T16:16:34Z", "created_at": "2016-12-22T09:08:47Z", "updated_at": "2023-03-31T05:39:22Z", "fetched_at": "2025-08-11T13:54:25.626Z"}, "hanami/contributors": {"name": "contributors", "full_name": "hanami/contributors", "description": "All hanami contributors in one place", "url": "https://github.com/hanami/contributors", "language": "<PERSON>", "stars": 14, "forks": 9, "size": 26704, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2023-06-07T00:25:26Z", "created_at": "2017-03-13T23:24:35Z", "updated_at": "2023-03-22T19:17:34Z", "fetched_at": "2025-08-11T13:54:29.288Z"}, "getsentry/sentry-docs": {"name": "sentry-docs", "full_name": "getsentry/sentry-docs", "description": "Sentry's documentation (and tools to build it)", "url": "https://github.com/getsentry/sentry-docs", "language": "MDX", "stars": 386, "forks": 1579, "size": 566407, "user_commits": 7, "total_commits": 13536, "user_contribution_percentage": 0.1, "last_commit_date": "2025-08-11T13:51:32Z", "created_at": "2015-04-12T22:59:45Z", "updated_at": "2025-08-11T13:51:37Z", "fetched_at": "2025-08-11T13:54:33.941Z"}, "solnic/drops_inflector": {"name": "drops_inflector", "full_name": "solnic/drops_inflector", "description": "Inflection utils for Elixir", "url": "https://github.com/solnic/drops_inflector", "language": "<PERSON><PERSON><PERSON>", "stars": 12, "forks": 1, "size": 53, "user_commits": 27, "total_commits": 100, "user_contribution_percentage": 27, "last_commit_date": "2025-07-30T07:32:07Z", "created_at": "2025-07-18T06:35:28Z", "updated_at": "2025-08-09T16:40:43Z", "fetched_at": "2025-08-11T13:54:37.812Z"}, "getsentry/sentry-ruby": {"name": "sentry-ruby", "full_name": "getsentry/sentry-ruby", "description": "Sentry SDK for Ruby", "url": "https://github.com/getsentry/sentry-ruby", "language": "<PERSON>", "stars": 978, "forks": 516, "size": 5069, "user_commits": 93, "total_commits": 186, "user_contribution_percentage": 50, "last_commit_date": "2025-07-31T14:44:11Z", "created_at": "2012-04-06T06:21:26Z", "updated_at": "2025-08-05T10:13:54Z", "fetched_at": "2025-08-11T13:54:41.477Z"}, "getsentry/sentry-elixir": {"name": "sentry-elixir", "full_name": "getsentry/sentry-elixir", "description": "The official Elixir <PERSON> for Sentry (sentry.io)", "url": "https://github.com/getsentry/sentry-elixir", "language": "<PERSON><PERSON><PERSON>", "stars": 663, "forks": 207, "size": 2091, "user_commits": 23, "total_commits": 100, "user_contribution_percentage": 23, "last_commit_date": "2025-07-16T11:28:09Z", "created_at": "2014-08-13T22:38:05Z", "updated_at": "2025-08-05T10:03:05Z", "fetched_at": "2025-08-11T13:54:45.514Z"}, "solnic/drops_relation": {"name": "drops_relation", "full_name": "solnic/drops_relation", "description": "🔋-included relation abstraction on top of Ecto with schema inference and composable query API + more ✨", "url": "https://github.com/solnic/drops_relation", "language": "<PERSON><PERSON><PERSON>", "stars": 26, "forks": 0, "size": 774, "user_commits": 194, "total_commits": 388, "user_contribution_percentage": 50, "last_commit_date": "2025-07-30T11:59:59Z", "created_at": "2025-07-08T23:33:30Z", "updated_at": "2025-08-04T01:16:22Z", "fetched_at": "2025-08-11T13:54:49.136Z"}, "solnic/virtus": {"name": "virtus", "full_name": "solnic/virtus", "description": "[DISCONTINUED ] Attributes on Steroids for Plain Old Ruby Objects", "url": "https://github.com/solnic/virtus", "language": "<PERSON>", "stars": 3760, "forks": 230, "size": 1853, "user_commits": 981, "total_commits": 1962, "user_contribution_percentage": 50, "last_commit_date": "2021-06-07T14:15:16Z", "created_at": "2011-04-02T16:23:50Z", "updated_at": "2025-08-03T16:43:51Z", "fetched_at": "2025-08-11T13:54:52.807Z"}, "dkubb/adamantium": {"name": "adamantium", "full_name": "dkubb/adamantium", "description": "Create immutable objects", "url": "https://github.com/dkubb/adamantium", "language": "<PERSON>", "stars": 370, "forks": 13, "size": 777, "user_commits": 4, "total_commits": 100, "user_contribution_percentage": 4, "last_commit_date": "2014-08-11T07:50:52Z", "created_at": "2012-08-04T22:17:25Z", "updated_at": "2025-08-03T11:21:16Z", "fetched_at": "2025-08-11T13:54:56.366Z"}, "solnic/text_parser": {"name": "text_parser", "full_name": "solnic/text_parser", "description": "TextParser is an Elixir library for extracting and validating structured tokens from text, such as URLs, hashtags, @-mentions etc.", "url": "https://github.com/solnic/text_parser", "language": "<PERSON><PERSON><PERSON>", "stars": 47, "forks": 0, "size": 63, "user_commits": 25, "total_commits": 100, "user_contribution_percentage": 25, "last_commit_date": "2025-03-21T09:14:08Z", "created_at": "2025-03-11T07:46:42Z", "updated_at": "2025-07-25T11:11:39Z", "fetched_at": "2025-08-11T13:54:59.998Z"}, "dkubb/ice_nine": {"name": "ice_nine", "full_name": "dkubb/ice_nine", "description": "Deep Freeze Ruby Objects", "url": "https://github.com/dkubb/ice_nine", "language": "<PERSON>", "stars": 312, "forks": 17, "size": 297, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2023-07-24T10:58:58Z", "created_at": "2012-03-21T05:55:13Z", "updated_at": "2025-07-18T15:41:38Z", "fetched_at": "2025-08-11T13:55:03.672Z"}, "dkubb/yardstick": {"name": "yardstick", "full_name": "dkubb/yardstick", "description": "A tool for verifying YARD documentation coverage", "url": "https://github.com/dkubb/yardstick", "language": "<PERSON>", "stars": 174, "forks": 16, "size": 476, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2015-12-21T06:42:41Z", "created_at": "2009-07-17T22:25:54Z", "updated_at": "2025-04-14T03:03:28Z", "fetched_at": "2025-08-11T13:55:07.271Z"}, "solnic/solnic": {"name": "solnic", "full_name": "solnic/solnic", "description": "solnic.codes website + my GitHub README.md in one repo", "url": "https://github.com/solnic/solnic", "language": null, "stars": 0, "forks": 3, "size": 2389, "user_commits": 65, "total_commits": 130, "user_contribution_percentage": 50, "last_commit_date": "2025-03-22T11:20:26Z", "created_at": "2020-12-05T18:51:15Z", "updated_at": "2025-03-22T11:20:31Z", "fetched_at": "2025-08-11T13:55:11.164Z"}, "shanna/dm-mongo-adapter": {"name": "dm-mongo-adapter", "full_name": "shanna/dm-mongo-adapter", "description": "Mongo DataMapper Adapter", "url": "https://github.com/shanna/dm-mongo-adapter", "language": "<PERSON>", "stars": 15, "forks": 14, "size": 106, "user_commits": 0, "total_commits": 100, "user_contribution_percentage": 0, "last_commit_date": "2010-02-23T04:36:07Z", "created_at": "2009-06-24T04:42:10Z", "updated_at": "2025-02-26T20:44:18Z", "fetched_at": "2025-08-11T13:55:14.706Z"}, "solnic/middleman-docsite": {"name": "middleman-docsite", "full_name": "solnic/middleman-docsite", "description": "Various middleman extensions extracted from rom-rb and dry-rb websites", "url": "https://github.com/solnic/middleman-docsite", "language": "<PERSON>", "stars": 3, "forks": 2, "size": 66, "user_commits": 42, "total_commits": 100, "user_contribution_percentage": 42, "last_commit_date": "2022-01-27T07:48:07Z", "created_at": "2019-09-29T10:38:56Z", "updated_at": "2025-01-12T00:24:10Z", "fetched_at": "2025-08-11T13:55:18.469Z"}, "dkubb/equalizer": {"name": "equalizer", "full_name": "dkubb/equalizer", "description": "Define equality, equivalency and hash methods automatically", "url": "https://github.com/dkubb/equalizer", "language": "<PERSON>", "stars": 202, "forks": 16, "size": 155, "user_commits": 2, "total_commits": 100, "user_contribution_percentage": 2, "last_commit_date": "2024-08-02T03:25:20Z", "created_at": "2012-08-30T15:16:06Z", "updated_at": "2025-01-05T21:14:39Z", "fetched_at": "2025-08-11T13:55:22.136Z"}, "sql-rb/sql-composer": {"name": "sql-composer", "full_name": "sql-rb/sql-composer", "description": "Standalone SQL composer <PERSON><PERSON> for Ruby", "url": "https://github.com/sql-rb/sql-composer", "language": "<PERSON>", "stars": 33, "forks": 3, "size": 88, "user_commits": 45, "total_commits": 100, "user_contribution_percentage": 45, "last_commit_date": "2020-12-25T06:45:58Z", "created_at": "2015-03-19T11:49:58Z", "updated_at": "2024-12-11T01:44:38Z", "fetched_at": "2025-08-11T13:55:25.831Z"}, "solnic/transproc": {"name": "transproc", "full_name": "solnic/transproc", "description": "The project was ported to dry-rb/dry-transformer", "url": "https://github.com/solnic/transproc", "language": "<PERSON>", "stars": 409, "forks": 28, "size": 456, "user_commits": 269, "total_commits": 538, "user_contribution_percentage": 50, "last_commit_date": "2019-12-28T12:29:22Z", "created_at": "2014-12-24T15:15:27Z", "updated_at": "2024-11-28T16:30:26Z", "fetched_at": "2025-08-11T13:55:29.335Z"}, "dkubb/axiom": {"name": "axiom", "full_name": "dkubb/axiom", "description": "Simplifies querying of structured data using relational algebra", "url": "https://github.com/dkubb/axiom", "language": "<PERSON>", "stars": 456, "forks": 20, "size": 6130, "user_commits": 4, "total_commits": 100, "user_contribution_percentage": 4, "last_commit_date": "2014-07-17T04:49:30Z", "created_at": "2010-01-04T05:29:51Z", "updated_at": "2024-10-31T08:43:16Z", "fetched_at": "2025-08-11T13:55:32.823Z"}, "solnic/dm-mongo-adapter": {"name": "dm-mongo-adapter", "full_name": "solnic/dm-mongo-adapter", "description": "MongoDB DataMapper Adapter", "url": "https://github.com/solnic/dm-mongo-adapter", "language": "<PERSON>", "stars": 72, "forks": 11, "size": 602, "user_commits": 85, "total_commits": 170, "user_contribution_percentage": 50, "last_commit_date": "2012-02-25T22:06:44Z", "created_at": "2009-08-25T18:33:45Z", "updated_at": "2024-09-21T18:16:37Z", "fetched_at": "2025-08-11T13:55:36.702Z"}, "solnic/transflow": {"name": "transflow", "full_name": "solnic/transflow", "description": "[DISCONTINUED] Business transaction flow DSL", "url": "https://github.com/solnic/transflow", "language": "<PERSON>", "stars": 101, "forks": 2, "size": 53, "user_commits": 75, "total_commits": 150, "user_contribution_percentage": 50, "last_commit_date": "2016-03-28T13:32:45Z", "created_at": "2015-08-16T10:54:24Z", "updated_at": "2024-07-24T15:48:14Z", "fetched_at": "2025-08-11T13:55:40.450Z"}}}