{"generated_at": "2025-08-11T00:00:00Z", "username": "solnic", "name": "<PERSON>", "total_contributions": 12847, "current_year_contributions": 1234, "total_repositories": 156, "public_repositories": 89, "organizations_count": 23, "organizations": [{"login": "dry-rb", "name": "dry-rb"}, {"login": "rom-rb", "name": "Ruby Object Mapper"}, {"login": "hanami", "name": "Hanami"}, {"login": "datamapper", "name": "DataMapper"}, {"login": "solnic", "name": "<PERSON>"}], "yearly_contributions": [{"year": 2024, "totalContributions": 1234}, {"year": 2023, "totalContributions": 1456}, {"year": 2022, "totalContributions": 1678}, {"year": 2021, "totalContributions": 1543}, {"year": 2020, "totalContributions": 1789}, {"year": 2019, "totalContributions": 1432}, {"year": 2018, "totalContributions": 1234}, {"year": 2017, "totalContributions": 1098}, {"year": 2016, "totalContributions": 987}, {"year": 2015, "totalContributions": 1396}], "repositories_sample": [{"name": "transproc", "owner": "solnic", "is_private": false}, {"name": "virtus", "owner": "solnic", "is_private": false}, {"name": "coercible", "owner": "solnic", "is_private": false}, {"name": "dry-validation", "owner": "dry-rb", "is_private": false}, {"name": "dry-types", "owner": "dry-rb", "is_private": false}, {"name": "rom", "owner": "rom-rb", "is_private": false}, {"name": "rom-sql", "owner": "rom-rb", "is_private": false}, {"name": "hanami", "owner": "hanami", "is_private": false}]}